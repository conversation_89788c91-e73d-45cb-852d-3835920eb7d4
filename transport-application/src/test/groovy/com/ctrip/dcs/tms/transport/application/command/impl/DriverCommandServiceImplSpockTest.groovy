package com.ctrip.dcs.tms.transport.application.command.impl

import com.ctrip.dcs.tms.transport.api.model.CategorySOADTO
import com.ctrip.dcs.tms.transport.application.command.CommonCommandService
import com.ctrip.dcs.tms.transport.application.command.DriverCommandService
import com.ctrip.dcs.tms.transport.application.command.DrvVehRecruitingCommandService
import com.ctrip.dcs.tms.transport.application.command.RecruitingCommandService
import com.ctrip.dcs.tms.transport.application.command.TmsQmqProducerCommandService
import com.ctrip.dcs.tms.transport.application.command.TmsTransportApproveCommandService
import com.ctrip.dcs.tms.transport.application.command.TmsVerifyEventCommandService
import com.ctrip.dcs.tms.transport.application.command.TransportGroupCommandService
import com.ctrip.dcs.tms.transport.application.query.AuthorizationCheckService
import com.ctrip.dcs.tms.transport.application.query.DriverPasswordService
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService
import com.ctrip.dcs.tms.transport.application.query.DrvVehRecruitingQueryService
import com.ctrip.dcs.tms.transport.application.query.QueryCategoryService
import com.ctrip.dcs.tms.transport.application.query.TmsPmsproductQueryService
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverGuideProxy
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupDriverRelationPO
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ApprovalProcessAuthQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ChangeRecordAttributeNameQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverseasQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TransportCommonQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.util.MobileHelper
import com.ctrip.dcs.tms.transport.infrastructure.common.util.SupplierDayProductLineMigrationHelper
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DriverGroupRelationRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDispatchRelationRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvFreezeRecordRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvHealthPunchRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvRecruitingRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository

import com.ctrip.dcs.tms.transport.infrastructure.port.repository.ModRecordRespository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsCertificateCheckRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvFreezeRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvLoginInformationRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsTransportApproveRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupDriverRelationRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository
import com.ctrip.igt.framework.common.result.Result
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants
import spock.lang.*

import java.sql.Timestamp

class DriverCommandServiceImplSpockTest extends Specification {
    def testObj = new DriverCommandServiceImpl()
    def drvDrvierRepository = Mock(DrvDrvierRepository)
    def tmsQmqProducerCommandService = Mock(TmsQmqProducerCommandService)
    def vehicleRepository = Mock(VehicleRepository)
    def modRecordRespository = Mock(ModRecordRespository)
    def pmsproductQueryService = Mock(TmsPmsproductQueryService)
    def drvRecruitingRepository = Mock(DrvRecruitingRepository)
    def driverQueryService = Mock(DriverQueryService)
    def recruitingCommandService = Mock(RecruitingCommandService)
    def transportGroupCommandService = Mock(TransportGroupCommandService)
    def changeRecordAttributeNameQconfig = Mock(ChangeRecordAttributeNameQconfig)
    def authQconfig = Mock(ApprovalProcessAuthQconfig)
    def commonCommandService = Mock(CommonCommandService)
    def enumRepository = Mock(EnumRepository)
    def tmsDrvFreezeRepository = Mock(TmsDrvFreezeRepository)
    def driverPasswordService = Mock(DriverPasswordService)
    def checkRepository = Mock(TmsCertificateCheckRepository)
    def driverGroupRelationRepository = Mock(DriverGroupRelationRepository)
    def productionLineUtil = Mock(ProductionLineUtil)
    def tspTransportGroupDriverRelationRepository = Mock(TspTransportGroupDriverRelationRepository)
    def transportGroupRepository = Mock(TransportGroupRepository)
    def approveRepository = Mock(TmsTransportApproveRepository)
    def approveCommandService = Mock(TmsTransportApproveCommandService)
    def qconfig = Mock(TmsTransportQconfig)
    def informationRepository = Mock(TmsDrvLoginInformationRepository)
    def drvVehRecruitingCommandService = Mock(DrvVehRecruitingCommandService)
    def drvHealthPunchRepository = Mock(DrvHealthPunchRepository)
    def drvVehRecruitingQueryService = Mock(DrvVehRecruitingQueryService)
    def authorizationCheckService = Mock(AuthorizationCheckService)
    def transportGroupQueryService = Mock(TransportGroupQueryService)
    def drvDispatchRelationRepository = Mock(DrvDispatchRelationRepository)
    def drvFreezeRecordRepository = Mock(DrvFreezeRecordRepository)
    def overseasQconfig = Mock(OverseasQconfig)
    def driverCommandService = Mock(DriverCommandService)
    def eventCommandService = Mock(TmsVerifyEventCommandService)
    def driverAccountManagementHelper = Mock(DriverAccountManagementHelper)
    def commonQconfig = Mock(TransportCommonQconfig)
    def mobileHelper = Mock(MobileHelper)
    def driverGuidProxy = Mock(DriverGuideProxy)
    def supplierDayProductLineMigrationHelper = Mock(SupplierDayProductLineMigrationHelper)
    def queryCategoryService = Mock(QueryCategoryService)

    def setup() {

        testObj.drvDrvierRepository = drvDrvierRepository
        testObj.tmsQmqProducerCommandService = tmsQmqProducerCommandService
        testObj.vehicleRepository = vehicleRepository
        testObj.modRecordRespository = modRecordRespository
        testObj.pmsproductQueryService = pmsproductQueryService
        testObj.drvRecruitingRepository = drvRecruitingRepository
        testObj.driverQueryService = driverQueryService
        testObj.recruitingCommandService = recruitingCommandService
        testObj.transportGroupCommandService = transportGroupCommandService
        testObj.changeRecordAttributeNameQconfig = changeRecordAttributeNameQconfig
        testObj.authQconfig = authQconfig
        testObj.commonCommandService = commonCommandService
        testObj.enumRepository = enumRepository
        testObj.tmsDrvFreezeRepository = tmsDrvFreezeRepository
        testObj.driverPasswordService = driverPasswordService
        testObj.checkRepository = checkRepository
        testObj.driverGroupRelationRepository = driverGroupRelationRepository
        testObj.productionLineUtil = productionLineUtil
        testObj.tspTransportGroupDriverRelationRepository = tspTransportGroupDriverRelationRepository
        testObj.transportGroupRepository = transportGroupRepository
        testObj.approveRepository = approveRepository
        testObj.approveCommandService = approveCommandService
        testObj.qconfig = qconfig
        testObj.informationRepository = informationRepository
        testObj.drvVehRecruitingCommandService = drvVehRecruitingCommandService
        testObj.drvHealthPunchRepository = drvHealthPunchRepository
        testObj.drvVehRecruitingQueryService = drvVehRecruitingQueryService
        testObj.authorizationCheckService = authorizationCheckService
        testObj.transportGroupQueryService = transportGroupQueryService
        testObj.drvDispatchRelationRepository = drvDispatchRelationRepository
        testObj.drvFreezeRecordRepository = drvFreezeRecordRepository
        testObj.overseasQconfig = overseasQconfig
        testObj.driverCommandService = driverCommandService
        testObj.eventCommandService = eventCommandService
        testObj.driverAccountManagementHelper = driverAccountManagementHelper
        testObj.commonQconfig = commonQconfig
        testObj.mobileHelper = mobileHelper
        testObj.driverGuidProxy = driverGuidProxy
        testObj.supplierDayProductLineMigrationHelper = supplierDayProductLineMigrationHelper
        testObj.queryCategoryService = queryCategoryService
    }


    @Unroll
    def "updateDrvUidTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        drvDrvierRepository.updateDrvUid(_, _, _, _, _) >> 0


        when:
        def result = testObj.updateDrvUid(drvId, uid, modifyUser, ppmAccount, qunarAccount)

        then: "验证返回结果里属性值是否符合预期"

        result.isSuccess() == expectedResult
        where: "表格方式验证多种分支调用场景"
        uid   | modifyUser   | drvId | qunarAccount   | ppmAccount   || expectedResult
        "uid" | "modifyUser" | 1L    | "qunarAccount" | "ppmAccount" || true
    }

    @Unroll
    def "updateDrvTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        and: "Mock相关接口返回"
        drvDrvierRepository.queryByPk(_) >> new DrvDriverPO(uid: "uid", temporaryDispatchMark: 0, ocrPassStatusJson: "ocrPassStatusJson", active: Boolean.TRUE, vehBindTime: new Timestamp(System.currentTimeMillis()), categorySynthesizeCode: 0, drvId: 1L, supplierId: 1L, cityId: 1L, igtCode: "igtCode", drvPhone: "drvPhone", vehicleId: 1L, vehicleLicense: "vehicleLicense", vehicleTypeId: 1L, drvHeadImg: "drvHeadImg", drvStatus: 0, internalScope: 0, driverNetCertNo: "driverNetCertNo")
        drvDrvierRepository.updateDrv(_) >> 0
        vehicleRepository.queryByPk(_) >> new VehVehiclePO(categorySynthesizeCode: 0, vehicleLicense: "vehicleLicense", supplierId: 1L, vehicleTypeId: 1L, hasDrv: Boolean.TRUE)
        productionLineUtil.getShowProductionLineList(_) >> [0]
        productionLineUtil.getDAYProductLineCode() >> 0
        productionLineUtil.bindTransportCheck(_, _) >> null
        productionLineUtil.checkBind(_, _, _) >> null
        productionLineUtil.isProductLineCodeCheck(_, _) >> Boolean.TRUE
        productionLineUtil.isProductLineCodeNewAddDayCheck(_, _) >> true
        productionLineUtil.isOnlyDayProductLine(_) >> true
        tspTransportGroupDriverRelationRepository.queryTransportGroupIdByDrvIds(_) >> [new TspTransportGroupDriverRelationPO(transportGroupId: 1L)]
        transportGroupRepository.queryDriverRelatedTransportGroupByModeList(_, _) >> [new TspTransportGroupPO(categorySynthesizeCode: 0, transportGroupName: "transportGroupName")]
        approveRepository.queryApproveCountByParams(_, _, _, _) >> 0
        approveCommandService.checkColumnApproveIng(_) >> Boolean.TRUE
        qconfig.getOverseasDefaultDrvHead() >> "getOverseasDefaultDrvHeadResponse"
        qconfig.getCheckBindFlag() >> Boolean.TRUE
        mobileHelper.isMobileValid(_, _, _) >>   Result.Builder.<Boolean>newResult().success().withCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE).build();
        queryCategoryService.getContractList(_, _) >> [new CategorySOADTO(id: 0)]

        and: "Spy相关接口"
        spy.updateApproveMethod(_, _, _, _, _, _, _, _) >> Boolean.TRUE
        spy.overseasExecuteEditApprove(_, _, _) >> Boolean.TRUE
        spy.updateDrvSuccessFollow(_, _, _, _, _, _) >> Boolean.TRUE
        spy.compareAttribute(_, _) >> null

        when:
        def result = spy.updateDrv(drvDriverPO, ocrHeadPortraitResult, ocrPassStatusList)

        then: "验证返回结果里属性值是否符合预期"

        result.code == expectedResult
        where: "表格方式验证多种分支调用场景"
        ocrPassStatusList                                                   | drvDriverPO                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | ocrHeadPortraitResult || expectedResult
        [new com.ctrip.dcs.tms.transport.api.model.OcrPassStatusModelSOA()] | new com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO(uid: "uid", temporaryDispatchMark: 0, ocrPassStatusJson: "ocrPassStatusJson", active: Boolean.TRUE, vehBindTime: new java.sql.Timestamp(java.lang.System.currentTimeMillis()), categorySynthesizeCode: 0, drvId: 1L, supplierId: 1L, cityId: 1L, igtCode: "igtCode", drvPhone: "drvPhone", vehicleId: 1L, vehicleLicense: "vehicleLicense", vehicleTypeId: 1L, drvHeadImg: "drvHeadImg", drvStatus: 0, internalScope: 0, driverNetCertNo: "driverNetCertNo") | Boolean.TRUE          || "500"
    }

}

