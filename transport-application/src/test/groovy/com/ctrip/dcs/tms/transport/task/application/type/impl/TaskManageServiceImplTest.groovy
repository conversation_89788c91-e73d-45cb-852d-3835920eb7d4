package com.ctrip.dcs.tms.transport.task.application.type.impl

import com.ctrip.dcs.tms.transport.api.model.IsNeedCreateTaskRequestType
import com.ctrip.dcs.tms.transport.api.model.QueryTaskConfigRequestType
import com.ctrip.dcs.tms.transport.api.model.SaveIsNeedCreateTaskRequestType
import com.ctrip.dcs.tms.transport.api.model.VehicleDispatchPhotoTaskConfigParamDTO
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil
import com.ctrip.dcs.tms.transport.task.application.type.TaskService
import com.ctrip.dcs.tms.transport.task.application.type.VehicleDispatchPhotoTaskService
import com.ctrip.dcs.tms.transport.task.infrastructure.common.enums.NeedCreateTaskEnum
import com.ctrip.dcs.tms.transport.task.infrastructure.common.enums.TaskTypeEnum
import com.ctrip.igt.framework.common.result.Result
import org.assertj.core.util.Lists
import spock.lang.Specification
import spock.lang.Unroll

class TaskManageServiceImplTest extends Specification {
    def testObj = new TaskManageServiceImpl()
    def taskService = Mock(TaskService)
    def taskServiceList = Mock(List)
    def vehicleDispatchPhotoTaskMock = Mock(VehicleDispatchPhotoTaskServiceImpl)

    def setup() {

        testObj.taskServiceList = taskServiceList
    }

    @Unroll
    def "queryTaskConfigTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getTaskService(_) >> vehicleDispatchPhotoTaskMock
        vehicleDispatchPhotoTaskMock.queryTaskConfig(_) >> Result.Builder.<String>newResult().success().build()
        when:
        def result = spy.queryTaskConfig(requestType)

        then: "验证返回结果里属性值是否符合预期"
        result.success == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                                                                              || expectedResult
        new QueryTaskConfigRequestType(taskType: "vehicle_dispatch_photo", taskConfigParam: JsonUtil.toJson(new VehicleDispatchPhotoTaskConfigParamDTO())) || true
    }

    @Unroll
    def "isNeedCreateTaskTest"() {
        given: "设定相关方法入参"
        taskServiceList.stream() >> Lists.newArrayList(taskService).stream()
        taskService.match(_ as TaskTypeEnum) >> true
        vehicleDispatchPhotoTaskMock.isNeedCreateTask(_) >> Result.Builder.<NeedCreateTaskEnum>newResult().success().withData(NeedCreateTaskEnum.NEED).build()
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getTaskService(_) >> vehicleDispatchPhotoTaskMock
        when:
        def result = spy.isNeedCreateTask(requestType)

        then: "验证返回结果里属性值是否符合预期"
        result.data == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                                           || expectedResult
        new IsNeedCreateTaskRequestType(taskType: "vehicle_dispatch_photo") || NeedCreateTaskEnum.NEED
    }

    @Unroll
    def "saveIsNeedCreateTaskTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getTaskService(_) >> new VehicleDispatchPhotoTaskServiceImpl()
        when:
        def result = spy.saveIsNeedCreateTask(requestType)

        then: "验证返回结果里属性值是否符合预期"
        result.data == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                                               || expectedResult
        new SaveIsNeedCreateTaskRequestType(taskType: "taskType") || true
    }

    @Unroll
    def "getTaskServiceTest"() {
        given: "设定相关方法入参"
        taskServiceList.stream() >> Lists.newArrayList(taskService).stream()
        taskService.match(_ as TaskTypeEnum) >> true
        when:
        def result = testObj.getTaskService(taskType)

        then: "验证返回结果里属性值是否符合预期"
        (result != null) == expectedResult
        where: "表格方式验证多种分支调用场景"
        taskType                            || expectedResult
        TaskTypeEnum.VEHICLE_DISPATCH_PHOTO || true
    }
}
