package com.ctrip.dcs.tms.transport.application.query.impl

import base.BaseTest
import com.ctrip.dcs.tms.transport.application.query.VehicleQueryService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.VehCacheDTO
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository
import com.ctrip.dcs.tms.transport.infrastructure.service.IQueryVehicleAttributeService
import com.ctrip.dcs.tms.transport.infrastructure.service.dto.VehicleBrandDTO
import com.ctrip.dcs.tms.transport.infrastructure.service.dto.VehicleColorDTO
import com.ctrip.dcs.tms.transport.infrastructure.service.dto.VehicleSeriesDTO
import com.ctrip.dcs.tms.transport.infrastructure.service.dto.VehicleTypeDTO
import spock.lang.Unroll

class QueryVehicleForSaasServiceTest extends BaseTest{
    def vehicleRepository = Mock(VehicleRepository)
    def vehicleQueryService = Mock(VehicleQueryService)
    def queryVehicleAttributeService = Mock(IQueryVehicleAttributeService)
    def service = new QueryVehicleForSaasService(vehicleRepository:vehicleRepository,vehicleQueryService:vehicleQueryService,queryVehicleAttributeService:queryVehicleAttributeService)

    @Unroll
    def "query"(){
        when:
        vehicleQueryService.queryVehCacheList(_) >> cacheVehicle
        VehVehiclePO vehiclePO = new VehVehiclePO()
        vehicleRepository.queryVehicleByIds(_) >> Arrays.asList(vehiclePO)
        queryVehicleAttributeService.queryVehicleBrand(_) >>  new VehicleBrandDTO()
        queryVehicleAttributeService.queryVehicleSeries(_) >> new VehicleSeriesDTO()
        queryVehicleAttributeService.queryVehicleType(_) >> new VehicleTypeDTO()
        queryVehicleAttributeService.queryVehicleColor(_) >> new VehicleColorDTO()
        def result = service.query(Arrays.asList(1L,2L));
        then:
        result.size() == size
        where:
        cacheVehicle || size
        new ArrayList() || 1
        getCacheVehicle() || 2
    }

    public List<VehCacheDTO> getCacheVehicle(){
        VehCacheDTO vehCacheDTO = new VehCacheDTO()
        vehCacheDTO.setCarTypeId(117)
        vehCacheDTO.setCarId(1)
        return Arrays.asList(vehCacheDTO)
    }
}
