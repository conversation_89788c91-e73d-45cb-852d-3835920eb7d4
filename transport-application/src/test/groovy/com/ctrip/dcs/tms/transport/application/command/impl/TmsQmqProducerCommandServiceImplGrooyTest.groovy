package com.ctrip.dcs.tms.transport.application.command.impl

import base.BaseTest
import com.ctrip.dcs.async.task.message.DataCompareMessage
import com.ctrip.dcs.tms.transport.infrastructure.adapter.messaging.qmq.TmsQmqProducer

class TmsQmqProducerCommandServiceImplGrooyTest extends BaseTest {

    def tmsQmqProducer = Mock(TmsQmqProducer)
    def tmsQmqProducerCommandServiceImpl = new TmsQmqProducerCommandServiceImpl(tmsQmqProducer:tmsQmqProducer)


    def "sendDataCompareNotice"() {
        given:
        expect:
        tmsQmqProducerCommandServiceImpl.sendDataCompareNotice(dataCompareMessage)
        assert expectedResult

        where:
        dataCompareMessage     || expectedResult
        new DataCompareMessage() || true
    }
}
