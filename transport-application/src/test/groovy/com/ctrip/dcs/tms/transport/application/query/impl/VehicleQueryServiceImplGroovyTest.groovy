package com.ctrip.dcs.tms.transport.application.query.impl

import base.BaseTest
import com.ctrip.arch.distlock.DLock
import com.ctrip.arch.distlock.DistributedLockService
import com.ctrip.dcs.geo.domain.value.City
import com.ctrip.dcs.tms.transport.application.common.MockExcutorService
import com.ctrip.dcs.tms.transport.application.query.CertificateCheckQueryService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO
import com.ctrip.dcs.tms.transport.infrastructure.common.config.DistributedLockConfig
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OverageDTO
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.VehCacheDTO
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverageQConfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.util.RedisUtils

import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository
import com.ctrip.dcs.vehicle.domain.value.SceneVehicleModel
import com.ctrip.igt.framework.common.concurrent.threadpool.CThreadPool
import com.google.common.collect.Lists
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito

class VehicleQueryServiceImplGroovyTest extends BaseTest {


    def tmsqconfg = Mock(TmsTransportQconfig)
    def lockConfig = Mock(DistributedLockConfig)
    def vehicleRepository = Mock(VehicleRepository)
    def enumRepository = Mock(EnumRepository)
    def checkQueryService = Mock(CertificateCheckQueryService)
    def config = Mock(CommonConfig)
    def productionLineUtil = Mock(ProductionLineUtil)
    def overageQConfig = Mock(OverageQConfig)
    def vehicleQueryServiceImpl = new VehicleQueryServiceImpl(tmsTransportQconfig: tmsqconfg, lockConfig: lockConfig, vehicleRepository: vehicleRepository, enumRepository: enumRepository, checkQueryService: checkQueryService, productionLineUtil: productionLineUtil, config: config,overageQConfig:overageQConfig)

    def "test supplementCache"() {
        given:
        List<VehCacheDTO> dtoList = Lists.newArrayList()
        VehCacheDTO drvCacheDTO = new VehCacheDTO()
        drvCacheDTO.setCarId(1L)
        dtoList.add(drvCacheDTO)
        tmsqconfg.getRedisTtransitionSwitch() >> false
        when:
        def res = vehicleQueryServiceImpl.supplementCache(dtoList)
        then:
        res == null
    }

    def "test supplementCache1"() {
        given:
        List<VehCacheDTO> dtoList = Lists.newArrayList()
        VehCacheDTO drvCacheDTO = new VehCacheDTO()
        drvCacheDTO.setCarId(1L)
        dtoList.add(drvCacheDTO)
        tmsqconfg.getRedisTtransitionSwitch() >> true
        lockConfig.getDistributedLockVehCache(_) >> null
        when:
        def res = vehicleQueryServiceImpl.supplementCache(dtoList)
        then:
        res == null
    }

    def "test queryVehCacheList"(){
        PowerMockito.mockStatic(RedisUtils.class)
        List<VehCacheDTO> drvCacheDTOList = Lists.newArrayList()
        VehCacheDTO drvCacheDTO = new VehCacheDTO()
        drvCacheDTO.setCarId(1L)
        drvCacheDTOList.add(drvCacheDTO)
        given:
        PowerMockito.when(RedisUtils.mGet(Mockito.anyList())).thenReturn(drvCacheDTOList)
        enumRepository.getVehicleType(_) >> SceneVehicleModel.builder().build()
        Set<Long> set = new HashSet<>()
        set.add(1L)
        set.add(2L)
        tmsqconfg.getRedisTtransitionSwitch() >> true
        checkQueryService.getCertificateCheckMap(_ as Set, _ as TmsTransportConstant.CertificateCheckTypeEnum, _ as List) >> [1L: [1: true]]
        vehicleRepository.queryVehInfo4Cache(_ as Set) >> [new VehVehiclePO(supplierId: 1L)]
        when:
        def res = vehicleQueryServiceImpl.queryVehCacheList(set)
        then:
        def ex = thrown(NullPointerException);
        ex instanceof NullPointerException
    }

    def "test queryVehCacheList1"(){
        PowerMockito.mockStatic(RedisUtils.class)
        List<VehCacheDTO> drvCacheDTOList = Lists.newArrayList()
        VehCacheDTO drvCacheDTO = new VehCacheDTO()
        drvCacheDTO.setCarId(1L)
        drvCacheDTO.setCityId(1L)
        drvCacheDTO.setCarTypeId(117)
        drvCacheDTO.setVehRegstDate("2024-12-10 10:23:45")
        drvCacheDTOList.add(drvCacheDTO)
        given:
        PowerMockito.when(RedisUtils.mGet(Mockito.anyList())).thenReturn(drvCacheDTOList)
        Set<Long> set = new HashSet<>()
        set.add(1L)
        set.add(2L)
        tmsqconfg.getRedisTtransitionSwitch() >> false
        config.getOverageGraySwitch() >> true
        config.getCityIdList() >> [1L]
        enumRepository.queryByCityIds(_) >> [City.builder().chineseMainland(true).id(1L).build()]
        overageQConfig.getOverageMap(_,_) >> new OverageDTO(cityId:1L,vehicleTypeId:117,accessLimit:4.75,overage:5)
        checkQueryService.getCertificateCheckMap(_ as Set, _ as TmsTransportConstant.CertificateCheckTypeEnum, _ as List) >> [1L: [1: true], 2L: [2: true]]
        vehicleRepository.queryVehInfo4Cache(_ as Set) >> [new VehVehiclePO(vehicleId:1L, supplierId: 1L)]

        when:
        def res = vehicleQueryServiceImpl.queryVehCacheList(set)
        then:
        def ex = thrown(IllegalArgumentException);
        ex instanceof IllegalArgumentException

    }


    def "test supplementCache3"() {
        given:
        List<VehCacheDTO> dtoList = Lists.newArrayList()
        VehCacheDTO drvCacheDTO = new VehCacheDTO()
        drvCacheDTO.setCarId(1L)
        dtoList.add(drvCacheDTO)
        tmsqconfg.getRedisTtransitionSwitch() >> false
        DLock dLock = Mock(DLock.class);
        dLock.tryLock(_, _) >> true
        lockConfig.getDistributedLockVehCache(_) >> dLock
        when:
        def res = vehicleQueryServiceImpl.supplementCache(dtoList)
        then:
        res == null
    }
}
