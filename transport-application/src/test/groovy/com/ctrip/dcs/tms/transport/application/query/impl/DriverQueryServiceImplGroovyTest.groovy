package com.ctrip.dcs.tms.transport.application.query.impl

import base.BaseTest
import com.ctrip.dcs.tms.transport.application.command.CommonCommandService
import com.ctrip.dcs.tms.transport.application.convert.DrvResourceConverter
import com.ctrip.dcs.tms.transport.application.query.CertificateCheckQueryService
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.http.DriverPointsQueryProxy
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.InfrastructureServiceClientProxy
import com.ctrip.dcs.tms.transport.infrastructure.common.config.DistributedLockConfig
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DrvCacheDTO
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ApprovalProcessAuthQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverseasQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.util.RedisUtils
import com.ctrip.dcs.tms.transport.infrastructure.common.util.SensitiveDataControl

import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDispatchRelationRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDriverLeaveRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvEpidemicPreventionControlInfoRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvFreezeRecordRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvVehLegendRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository

import com.ctrip.dcs.tms.transport.infrastructure.port.repository.OverseasOcrRecordRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvFreezeRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvInactiveReasonRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupDriverRelationRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository
import com.google.common.collect.Lists
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito

/**
 * <AUTHOR> ZhangZhen
 * @create 2023/6/29 17:29
 */
class DriverQueryServiceImplGroovyTest extends BaseTest {

    def repository = Mock(DrvDrvierRepository)
    def enumRepository = Mock(EnumRepository)
    def relationRepository = Mock(TspTransportGroupDriverRelationRepository)
    def vehicleRepository = Mock(VehicleRepository)
    def groupRepository = Mock(TransportGroupRepository)
    def authQconfig = Mock(ApprovalProcessAuthQconfig)
    def checkQueryService = Mock(CertificateCheckQueryService)
    def transportQconfig = Mock(TmsTransportQconfig)
    def productionLineUtil = Mock(ProductionLineUtil)
    def tmsDrvFreezeRepository = Mock(TmsDrvFreezeRepository)
    def leaveRepository = Mock(DrvDriverLeaveRepository)
    def commonCommandService = Mock(CommonCommandService)
    def drvEpidemicPreventionControlInfoRepository = Mock(DrvEpidemicPreventionControlInfoRepository)
    def drvResourceConverter = Mock(DrvResourceConverter)
    def control = Mock(SensitiveDataControl)
    def transportGroupQueryService = Mock(TransportGroupQueryService)
    def drvDispatchRelationRepository = Mock(DrvDispatchRelationRepository)
    def drvFreezeRecordRepository = Mock(DrvFreezeRecordRepository)
    def overseasOcrRecordRepository = Mock(OverseasOcrRecordRepository)
    def overseasQconfig = Mock(OverseasQconfig)
    def drvVehLegendRepository = Mock(DrvVehLegendRepository)
    def infrastructureServiceClientProxy = Mock(InfrastructureServiceClientProxy)
    def lockConfig = Mock(DistributedLockConfig)
    def drvInactiveReasonRepository = Mock(TmsDrvInactiveReasonRepository)

    def driverQueryServiceImpl = new DriverQueryServiceImpl(
            repository: repository,
            enumRepository: enumRepository,
            relationRepository: relationRepository,
            vehicleRepository: vehicleRepository,
            groupRepository: groupRepository,
            authQconfig: authQconfig,
            checkQueryService: checkQueryService,
            transportQconfig: transportQconfig,
            productionLineUtil: productionLineUtil,
            tmsDrvFreezeRepository: tmsDrvFreezeRepository,
            leaveRepository: leaveRepository,
            commonCommandService: commonCommandService,
            drvEpidemicPreventionControlInfoRepository: drvEpidemicPreventionControlInfoRepository,
            drvResourceConverter: drvResourceConverter,
            control: control,
            transportGroupQueryService: transportGroupQueryService,
            drvDispatchRelationRepository: drvDispatchRelationRepository,
            drvFreezeRecordRepository: drvFreezeRecordRepository,
            overseasOcrRecordRepository: overseasOcrRecordRepository,
            overseasQconfig: overseasQconfig,
            drvVehLegendRepository: drvVehLegendRepository,
            infrastructureServiceClientProxy: infrastructureServiceClientProxy,
            lockConfig:lockConfig,
            drvInactiveReasonRepository: drvInactiveReasonRepository
    )

    def "test supplementCache"(){
        given:
        List<DrvCacheDTO> dtoList = Lists.newArrayList()
        DrvCacheDTO drvCacheDTO = new DrvCacheDTO()
        drvCacheDTO.setDriverId(1L)
        dtoList.add(drvCacheDTO)
        transportQconfig.getRedisTtransitionSwitch() >> false
        when:
        def res = driverQueryServiceImpl.supplementCache(dtoList)
        then:
        res == null
    }

    def "test supplementCache1"(){
        given:
        List<DrvCacheDTO> dtoList = Lists.newArrayList()
        DrvCacheDTO drvCacheDTO = new DrvCacheDTO()
        drvCacheDTO.setDriverId(1L)
        dtoList.add(drvCacheDTO)
        transportQconfig.getRedisTtransitionSwitch() >> true
        when:
        def res = driverQueryServiceImpl.supplementCache(dtoList)
        then:
        res == null
    }


    def "test queryDrvCacheList"(){
        PowerMockito.mockStatic(RedisUtils.class)
        List<DrvCacheDTO> drvCacheDTOList = Lists.newArrayList()
        DrvCacheDTO drvCacheDTO = new DrvCacheDTO()
        drvCacheDTOList.add(drvCacheDTO)
        given:
        PowerMockito.when(RedisUtils.mGet(Mockito.anyList())).thenReturn(drvCacheDTOList)
        Set<Long> set = new HashSet<>()
        set.add(1L)
        transportQconfig.getRedisTtransitionSwitch() >> true
        when:
        def res = driverQueryServiceImpl.queryDrvCacheList(set)
        then:
        res != null
    }

    def "test queryDrvCacheList1"(){
        PowerMockito.mockStatic(RedisUtils.class)
        List<DrvCacheDTO> drvCacheDTOList = Lists.newArrayList()
        DrvCacheDTO drvCacheDTO = new DrvCacheDTO()
        drvCacheDTOList.add(drvCacheDTO)
        given:
        PowerMockito.when(RedisUtils.mGet(Mockito.anyList())).thenReturn(drvCacheDTOList)
        Set<Long> set = new HashSet<>()
        set.add(1L)
        transportQconfig.getRedisTtransitionSwitch() >> false
        when:
        def res = driverQueryServiceImpl.queryDrvCacheList(set)
        then:
        res != null
    }

}
