package com.ctrip.dcs.tms.transport.application.convert;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.api.regulation.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import org.apache.commons.collections.*;
import org.assertj.core.util.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.powermock.api.mockito.*;
import org.powermock.core.classloader.annotations.*;
import org.powermock.modules.junit4.*;

import java.util.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({PlatformUtil.class, TmsTransUtil.class})
@PowerMockIgnore({"javax.management.*", "javax.script.*", "com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*",
        "org.w3c.dom.*", "javax.crypto.*"})
public class HistoryDrvResourceConverterTest {

    @InjectMocks
    private HistoryDrvResourceConverter converter;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(TmsTransUtil.class);
    }

    private DrvHistoryDriverPO getDrvHistoryDriverPO() {
        DrvHistoryDriverPO driverPO = new DrvHistoryDriverPO();
        driverPO.setDrvId(1L);
        driverPO.setDrvName("2");
        driverPO.setDrvPhone("3");
        driverPO.setCoopMode(4);
        driverPO.setDrvStatus(5);
        driverPO.setDrvIdcard("6");
        driverPO.setBankName("7");
        driverPO.setBankAccout("8");
        driverPO.setPpmAccout("9");
        driverPO.setInternalScope(10);
        driverPO.setDataMigrated(0);
        return driverPO;
    }

    @Test
    public void convertOldDriverTestEmptyCase() {
        Assert.assertNull(converter.convertOldDriver((DrvHistoryDriverPO) null));
    }

    @Test
    public void convertOldDriverTestNormalCase() {
        DrvHistoryDriverPO driverPO = getDrvHistoryDriverPO();
        OldDriverInfo info = converter.convertOldDriver(driverPO);
        assertConvertOldDriver(driverPO, info);
        Assert.assertNotNull(info);
    }

    @Test
    public void convertOldDriverTestNormalCase1() {
        DrvHistoryDriverPO driverPO = getDrvHistoryDriverPO();
        driverPO.setDataMigrated(null);
        OldDriverInfo info = converter.convertOldDriver(driverPO);
        assertConvertOldDriver(driverPO, info);
        Assert.assertNotNull(info);
    }

    @Test
    public void convertOldDriverListEmptyCase() {
        List<OldDriverInfo> infoList = converter.convertOldDriver((List<DrvHistoryDriverPO>) null);
        Assert.assertNotNull(infoList);
        Assert.assertTrue(CollectionUtils.isEmpty(infoList));
    }

    @Test
    public void convertOldDriverListNormalCase() {
        List<DrvHistoryDriverPO> list = Lists.newArrayList(getDrvHistoryDriverPO());
        List<OldDriverInfo> infoList = converter.convertOldDriver(list);
        Assert.assertNotNull(infoList);
        Assert.assertTrue(infoList.size() == list.size());
        for (int i = 0; i < list.size(); i++) {
            assertConvertOldDriver(list.get(i), infoList.get(i));
        }
    }

    @Test
    public void convertConditionTest() {
        QueryHistoryDrvDataRequestType requestType = new QueryHistoryDrvDataRequestType();
        requestType.setDrvPhone("1");
        requestType.setDrvId(2L);
        QueryHistoryDrvConditionDTO conditionDTO = converter.convertCondition(requestType);
        Assert.assertNotNull(conditionDTO);
        Assert.assertNotNull(conditionDTO.getDriverPhoneList());
        Assert.assertNotNull(conditionDTO.getDrvIdList());
        Assert.assertNotNull(conditionDTO.getFields());
        Assert.assertTrue(conditionDTO.getDrvIdList().get(0).longValue() == requestType.getDrvId().longValue());
    }

    private void assertConvertOldDriver(DrvHistoryDriverPO driverPO, OldDriverInfo info) {
        Assert.assertNotNull(info);
        Assert.assertNotNull(driverPO);
        Assert.assertEquals(info.getDrvId(), driverPO.getDrvId());
        Assert.assertEquals(info.getDrvName(), driverPO.getDrvName());
        Assert.assertEquals(info.getDrvPhone(), driverPO.getDrvPhone());
        Assert.assertEquals(info.getCoopMode(), driverPO.getCoopMode());
        Assert.assertEquals(info.getDrvStatus(), driverPO.getDrvStatus());
        Assert.assertEquals(info.getDrvIdcard(), driverPO.getDrvIdcard());
        Assert.assertEquals(info.getBankName(), driverPO.getBankName());
        Assert.assertEquals(info.getBankAccout(), driverPO.getBankAccout());
        Assert.assertEquals(info.getPpmAccount(), driverPO.getPpmAccout());
        Assert.assertEquals(info.getInternalScope(), driverPO.getInternalScope());
    }

    @Test
    public void convertPOTest() {
        AddHistoryDrvDataRequestType type = new AddHistoryDrvDataRequestType();
        converter.convertPO(type);
        Assert.assertNotNull(type);
    }

}
