package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.parser.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.base.PageHolder;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.dal.DalRepository;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.sql.*;
import java.util.Date;
import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class TmsModRecordQueryServiceImplTest {

    @InjectMocks
    private TmsModRecordQueryServiceImpl tmsModRecordQueryService;

    @Mock
    private ModRecordRespository<TmsModRecordPO> modRecordRespository;

    @Mock
    private GenericTmsModRecordParser genericTmsModRecordParser;

    @Mock
    TransportGroupRepository transportGroupRepository;

    @Mock
    DrvDrvierRepository drvDrvierRepository;

    @Mock
    VehicleRepository vehicleRepository;
    @Mock
    DrvRecruitingRepository drvRecruitingRepository;

    @Mock
    VehicleRecruitingRepository vehicleRecruitingRepository;
    @Mock
    DrvDispatchRelationRepository drvDispatchRelationRepository;

    //@Before
    public void setUp() throws Exception {
        List<TmsModRecordPO> tmsModRecordPOList = Lists.newArrayList();
        TmsModRecordPO tmsModRecordPO = new TmsModRecordPO();
        tmsModRecordPO.setId(1L);
        tmsModRecordPO.setModType(1);
        tmsModRecordPO.setModifyUser("aa");
        tmsModRecordPO.setDatachangeCreatetime(new Timestamp(new Date().getTime()));
        tmsModRecordPO.setDatachangeLasttime(new Timestamp(new Date().getTime()));
        tmsModRecordPO.setRrdId(1L);
        tmsModRecordPO.setRrdType(10);
        tmsModRecordPOList.add(tmsModRecordPO);

        Mockito.when(modRecordRespository.queryModRecordList(Mockito.anyLong(),Mockito.anyInt(),Mockito.anyLong())).thenReturn(tmsModRecordPOList);

        List<TmsModRecordVO> tmsModRecordVOList = Lists.newArrayList();
        TmsModRecordVO tmsModRecordVO = new TmsModRecordVO();
        tmsModRecordVO.setId(1);
        tmsModRecordVO.setModType(1);
        tmsModRecordVO.setModTypeName("aa");
        tmsModRecordVO.setModContent(Lists.newArrayList());
        tmsModRecordVO.setModUser("11");
        tmsModRecordVO.setDatachangeCreatetime("2020-01-01 00:00:00");
        tmsModRecordVO.setDatachangeLasttime("2020-01-01 00:00:00");
        tmsModRecordVOList.add(tmsModRecordVO);
        Mockito.when(genericTmsModRecordParser.parse(Mockito.anyList())).thenReturn(tmsModRecordVOList);
    }

    @Test
    public void queryModRecordList() {
        ModRecordSOARequestType modRecordSOARequestType = new ModRecordSOARequestType();
        modRecordSOARequestType.setRrdId(1L);
        modRecordSOARequestType.setRrdType(10);
        modRecordSOARequestType.setSupplierId(1L);
        Result<List<ModRecordSOADTO>>  result = tmsModRecordQueryService.queryModRecordList(modRecordSOARequestType);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testPreCheck() {
        ModRecordSOARequestType requestType = new ModRecordSOARequestType();
        requestType.setRrdType(10000);
        requestType.setRrdId(1002874L);
        Result<Void> t1 = tmsModRecordQueryService.preCheck(requestType);
        Assert.assertTrue(t1.isSuccess());

        requestType.setSupplierId(111L);
        Result<Void> t2 = tmsModRecordQueryService.preCheck(requestType);
        Assert.assertTrue(t2.isSuccess());


        requestType.setRrdType(1);
        TspTransportGroupPO tgrop = new TspTransportGroupPO();
        tgrop.setSupplierId(110L);
        DalRepository<TspTransportGroupPO> tspTransportGroupRepo = Mockito.mock(DalRepository.class);
        Mockito.when(transportGroupRepository.getTspTransportGroupRepo()).thenReturn(tspTransportGroupRepo);
        Mockito.when(tspTransportGroupRepo.queryByPk(Mockito.anyLong())).thenReturn(tgrop);
        Result<Void> tFail = tmsModRecordQueryService.preCheck(requestType);
        Assert.assertFalse(tFail.isSuccess());

        requestType.setRrdType(2);
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setSupplierId(111L);
        Mockito.when(drvDrvierRepository.queryByPk(Mockito.anyLong())).thenReturn(drvDriverPO);
        Result<Void> t3 = tmsModRecordQueryService.preCheck(requestType);
        Assert.assertTrue(t3.isSuccess());

        requestType.setRrdType(3);
        VehVehiclePO vehVehiclePO = new VehVehiclePO();
        vehVehiclePO.setSupplierId(111L);
        Mockito.when(vehicleRepository.queryByPk(Mockito.anyLong())).thenReturn(vehVehiclePO);
        Result<Void> t4 = tmsModRecordQueryService.preCheck(requestType);
        Assert.assertTrue(t4.isSuccess());

        requestType.setRrdType(4);
        DrvRecruitingPO recruitingPO = new DrvRecruitingPO();
        recruitingPO.setSupplierId(111L);
        Mockito.when(drvRecruitingRepository.queryByPK(Mockito.anyLong())).thenReturn(recruitingPO);
        Result<Void> t5 = tmsModRecordQueryService.preCheck(requestType);
        Assert.assertTrue(t5.isSuccess());

        requestType.setRrdType(7);
        VehicleRecruitingPO vehicleRecruitingPO = new VehicleRecruitingPO();
        vehicleRecruitingPO.setSupplierId(111L);
        Mockito.when(vehicleRecruitingRepository.queryByPK(Mockito.anyLong())).thenReturn(vehicleRecruitingPO);
        Result<Void> t6 = tmsModRecordQueryService.preCheck(requestType);
        Assert.assertTrue(t6.isSuccess());

//        requestType.setRrdType(13);
//        DrvDispatchRelationPO drvDispatchRelationPO = new DrvDispatchRelationPO();
//        drvDispatchRelationPO.setSupplierId(111L);
//        DalRepository<DrvDispatchRelationPO> dispatchRelationPODalRepository = Mockito.mock(DalRepository.class);
//        Mockito.when(drvDispatchRelationRepository.getDrvDispatchRelationRepo()).thenReturn(dispatchRelationPODalRepository);
//        Mockito.when(dispatchRelationPODalRepository.queryByPk(Mockito.anyLong())).thenReturn(drvDispatchRelationPO);
//        Result<Void> t7 = tmsModRecordQueryService.preCheck(requestType);
//        Assert.assertTrue(t7.isSuccess());
    }

}