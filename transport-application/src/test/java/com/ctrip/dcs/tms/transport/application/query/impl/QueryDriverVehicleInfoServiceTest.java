package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.application.dto.DriverVehicleDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvInfoPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class QueryDriverVehicleInfoServiceTest {
    @InjectMocks
    QueryDriverVehicleInfoService service;
    @Mock
    private DrvDrvierRepository driverRepository;
    @Mock
    private VehicleRepository vehicleRepository;
    @Test
    public void test()throws Exception{
        DrvDriverPO drvInfoPO = new DrvDriverPO();
        drvInfoPO.setDrvcardImg("DrvcardImg");
        drvInfoPO.setVehicleId(222L);
        Mockito.when(driverRepository.queryByPk(Mockito.any())).thenReturn(drvInfoPO);
        VehVehiclePO vehVehiclePO = new VehVehiclePO();
        vehVehiclePO.setVehicleFullImg("VehicleFullImg");
        Mockito.when(vehicleRepository.queryByPk(Mockito.any())).thenReturn(vehVehiclePO);
        DriverVehicleDTO driverVehicleDTO = service.query(111L);
        Assert.assertTrue(driverVehicleDTO.getDrvcardImg()!=null);
        Assert.assertTrue(driverVehicleDTO.getDrvcardImg().equals("DrvcardImg"));
        Assert.assertTrue(driverVehicleDTO.getVehicleFullImg().equals("VehicleFullImg"));
    }
}
