package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class AuthorizationCheckServiceImplTest {

    @InjectMocks
    AuthorizationCheckServiceImpl service;

    @Test
    public void operationAuthJudge() {
        Map<String, String> params = Maps.newHashMap();
        params.put("accountType","2");
        SessionHolder.setSessionSource(params);
        service.operationAuthJudge(Arrays.asList("111"),"111");
    }
}
