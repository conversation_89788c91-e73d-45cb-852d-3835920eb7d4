package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.sql.*;
import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class DrvFreezeQueryServiceImplTest {

    @InjectMocks
    private DrvFreezeQueryServiceImpl drvFreezeQueryService;
    @Mock
    EnumRepository enumRepository;
    @Mock
    DrvDrvierRepository repository;
    @Mock
    VehicleRepository vehicleRepository;
    @Mock
    TmsDrvFreezeRepository freezeRepository;
    @Mock
    TspTransportGroupDriverRelationRepository relationRepository;

    @Mock
    private SensitiveDataControl control;

    private Set<Long> drvSet = Sets.newHashSet(1L,2L,3L);

    @Before
    public void setUp() {
        List<DrvDriverPO> driverPOList = Lists.newArrayList();
        DrvDriverPO driverPO = new DrvDriverPO();
        driverPO.setDrvId(1L);
        driverPOList.add(driverPO);
        Mockito.when(repository.queryDrvList(Lists.newArrayList(drvSet))).thenReturn(driverPOList);
        List<TmsDrvFreezePO> drvFreezePOS = Lists.newArrayList();
        TmsDrvFreezePO freezePO = new TmsDrvFreezePO();
        freezePO.setFreezeStatus(2);
        freezePO.setFirstFreezeTime(new Timestamp(System.currentTimeMillis()));
        freezePO.setFreezeHour(1);
        drvFreezePOS.add(freezePO);
        Mockito.when(freezeRepository.queryDrvFreezeByDrvIds(Sets.newHashSet(1L))).thenReturn(drvFreezePOS);
    }

    @Test
    public void queryDrvFreezeInfoListTest() {
        List<DrvFreezeInfoSOADTO> list = drvFreezeQueryService.queryDrvFreezeInfoList(drvSet);
        Assert.assertTrue(!list.isEmpty());
    }

    @Test
    public void queryDrvFreezeInfoListTest1() {
        Mockito.when(repository.queryDrvList(Lists.newArrayList(drvSet))).thenReturn(Lists.newArrayList());
        List<DrvFreezeInfoSOADTO>  lst = drvFreezeQueryService.queryDrvFreezeInfoList(drvSet);
        Assert.assertTrue(lst.isEmpty());
    }

    @Test
    public void te() {
        TmsDrvFreezePO freezePO = new TmsDrvFreezePO();
        freezePO.setDrvId(1L);
        Mockito.when(freezeRepository.queryByPk(1L)).thenReturn(freezePO);
        DrvDriverPO driverPO = new DrvDriverPO();
        driverPO.setDrvId(1L);
        Mockito.when(repository.queryByPk(1L)).thenReturn(driverPO);
        Result<QueryDrvFreezeDetailDTOSOA> result = drvFreezeQueryService.queryDrvFreezeDetail(1L);
        Assert.assertTrue(result != null);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getData() != null);
        Assert.assertTrue(result.getData().getDrvId() == 1L);

    }

}