package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.impl.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class AuthorizationCheckServiceTest {

    @InjectMocks
    private AuthorizationCheckServiceImpl authorizationCheckService;

    @Mock
    private VehicleRecruitingRepository vehicleRecruitingRepository;

    @Mock
    private TransportGroupRepository transportGroupRepository;

    @Mock
    private DrvRecruitingRepository drvRecruitingRepository;

    @Mock
    private DrvDrvierRepository drvDrvierRepository;

    @Mock
    private VehicleRepository vehicleRepository;
    @Mock
    SelfServiceProviderConfig selfServiceProviderConfig;
    @Mock
    private CommonCommandService commandService;

    @Test
    public void checkAuthorizationDriverTest() {
        authorizationCheckService.checkAuthorizationDriver(null,0L);
        boolean b = authorizationCheckService.checkAuthorizationDriver(ImmutableList.of(12L,32L),0L);
        Assert.assertTrue(!b);
    }

    @Test
    public void checkAuthorizationDrvVehRecruitingTest() {
        authorizationCheckService.checkAuthorizationDrvVehRecruiting(null,0L);
        boolean b =  authorizationCheckService.checkAuthorizationDrvVehRecruiting(ImmutableList.of(12L,32L),0L);
        Assert.assertTrue(!b);
    }

    @Test
    public void checkAuthorizationTransportGroupTest() {
        authorizationCheckService.checkAuthorizationTransportGroup(null,0L);
        boolean b =  authorizationCheckService.checkAuthorizationTransportGroup(ImmutableList.of(12L,32L),0L);
        Assert.assertTrue(!b);
    }

    @Test
    public void checkAuthorizationVehicleTest() {
        authorizationCheckService.checkAuthorizationVehicle(null,0L);
        boolean b =  authorizationCheckService.checkAuthorizationVehicle(ImmutableList.of(12L,32L),0L);
        Assert.assertTrue(!b);
    }

    @Test
    public void checkAuthorizationVehicleRecruitingTest() {
        authorizationCheckService.checkAuthorizationVehicleRecruiting(null,0L);
        boolean b =  authorizationCheckService.checkAuthorizationVehicleRecruiting(ImmutableList.of(12L,32L),0L);
        Assert.assertTrue(!b);
    }

    @Test
    public void decisionRouteTest() {
        List<SelfServiceProviderModel> providerModels = Lists.newArrayList();
        Mockito.when(commandService.judgeSupplierIsZY(0L)).thenReturn(true);
        Mockito.when(selfServiceProviderConfig.getSelfServiceProviders()).thenReturn(providerModels);
        authorizationCheckService.decisionRoute(1,ImmutableList.of(12L,32L),0L);
        authorizationCheckService.decisionRoute(2,ImmutableList.of(12L,32L),0L);
        authorizationCheckService.decisionRoute(3,ImmutableList.of(12L,32L),0L);
        authorizationCheckService.decisionRoute(4,ImmutableList.of(12L,32L),0L);
        authorizationCheckService.decisionRoute(5,ImmutableList.of(12L,32L),0L);
        boolean b =  authorizationCheckService.decisionRoute(7,ImmutableList.of(12L,32L),0L);
        Assert.assertTrue(!b);
    }

}