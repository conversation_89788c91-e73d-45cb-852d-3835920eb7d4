package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.PaginatorDTO;
import com.ctrip.igt.framework.common.base.PageHolder;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

@RunWith(MockitoJUnitRunner.class)
public class RecruitingQueryServiceImplTest {

    @InjectMocks
    private RecruitingQueryServiceImpl recruitingQueryService;

    @Mock
    private VehicleRecruitingRepository vehicleRecruitingRepository;
    @Mock
    private DrvRecruitingRepository drvRecruitingRepository;
    @Mock
    private VehicleRepository vehicleRepository;
    @Mock
    private DrvDrvierRepository drvDrvierRepository;
    @Mock
    private EnumRepository enumRepository;
    @Mock
    private ApprovalProcessAuthQconfig approvalProcessAuthQconfig;
    @Mock
    private ProductionLineUtil productionLineUtil;
    @Mock
    private TmsRecruitingApproveStepRepository stepRepository;
    @Mock
    private TmsRecruitingApproveStepChildRepository childRepository;
    @Mock
    private CertificateCheckQueryService checkQueryService;
    @Mock
    private TmsApproveStepRecordRespository recordRespository;
    @Mock
    private TmsTransportQconfig qconfig;

    @Test
    public void dealWithEncryptData() {
        RecruitingSOARequestDTO recruitingSOARequestDTO = new RecruitingSOARequestDTO();
        recruitingSOARequestDTO.setDrvIdcard("1111");
        recruitingSOARequestDTO.setDrvPhone("135323424");
        recruitingQueryService.dealWithEncryptData(recruitingSOARequestDTO);
        Assert.assertTrue(true);
    }

    @Test
    public void queryApproveStep() {
        QueryApproveStepSOARequestType soaRequestType = new QueryApproveStepSOARequestType();
        soaRequestType.setApproveSourceId(1L);
        soaRequestType.setApproveType(1);
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setCityId(1L);
        drvRecruitingPO.setBdApproveStatus(1);
        drvRecruitingPO.setApproverStatus(4);
        drvRecruitingPO.setDrvFrom(2);
        drvRecruitingPO.setVehicleId(1L);
        Mockito.when(drvRecruitingRepository.queryByPK(1L)).thenReturn(drvRecruitingPO);
        List<TmsRecruitingApproveStepPO> stepPOList = Lists.newArrayList();
        TmsRecruitingApproveStepPO tmsRecruitingApproveStepPO = new TmsRecruitingApproveStepPO();
        tmsRecruitingApproveStepPO.setId(1L);
        tmsRecruitingApproveStepPO.setApproveItem(1);
        tmsRecruitingApproveStepPO.setApproveStatus(1);
        tmsRecruitingApproveStepPO.setApproveFrom(1);
        tmsRecruitingApproveStepPO.setApproveTime(DateUtil.getNow());
        tmsRecruitingApproveStepPO.setDatachangeLasttime(DateUtil.getNow());
        stepPOList.add(tmsRecruitingApproveStepPO);
        Map<Integer, TmsCertificateCheckPO> checkPOMap = Maps.newHashMap();
        TmsCertificateCheckPO tmsCertificateCheckPO = new TmsCertificateCheckPO();
        tmsCertificateCheckPO.setCheckStatus(1);
        tmsCertificateCheckPO.setCheckId(1L);
        tmsCertificateCheckPO.setCheckType(1);
        tmsCertificateCheckPO.setCertificateType(1);
        checkPOMap.put(1, tmsCertificateCheckPO);
        List<TmsRecruitingApproveStepChildPO> childPOList = Lists.newArrayList();
        TmsRecruitingApproveStepChildPO childPO = new TmsRecruitingApproveStepChildPO();
        childPO.setRecruitingApproveStepId(1L);
        childPO.setChildItem(1);
        childPO.setCheckStatus(1);
        childPOList.add(childPO);
        Result<QueryApproveStepSOAResponseType> responseTypeResult = recruitingQueryService.queryApproveStep(soaRequestType);
        Assert.assertTrue(responseTypeResult.isSuccess());
    }

    @Test
    public void queryRecruitingApproveStatus() throws SQLException {
        QueryRecruitingApproveStatusSOARequestType soaRequestType = new QueryRecruitingApproveStatusSOARequestType();
        soaRequestType.setDrvRecruitingId(1L);
        soaRequestType.setDrvPhone("1");
        List<DrvRecruitingPO> drvRecruitingPOList = Lists.newArrayList();
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setDrvRecruitingId(1L);
        drvRecruitingPO.setVersionFlag(3);
        drvRecruitingPOList.add(drvRecruitingPO);
        Mockito.when(drvRecruitingRepository.queryRecruitingDrvByPhone("1", 1L)).thenReturn(drvRecruitingPOList);
        Result<Integer> result = recruitingQueryService.queryRecruitingApproveStatus(soaRequestType);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void getChildItemList() throws SQLException {
        List<QueryApproveStepChildSOADTO> resultChildItemList = Lists.newArrayList();
        QueryApproveStepChildSOADTO childSOADTO = new QueryApproveStepChildSOADTO();
        childSOADTO.setParentStepId(1L);
        childSOADTO.setCheckStatus(1);
        childSOADTO.setChildItem(1);
        resultChildItemList.add(childSOADTO);
        Map<Integer, TmsCertificateCheckPO> drvCheckPOMap = Maps.newHashMap();
        TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
        checkPO.setCheckId(1L);
        checkPO.setCheckType(1);
        checkPO.setCertificateType(5);
        checkPO.setCheckStatus(1);
        drvCheckPOMap.put(5, checkPO);
        List<QueryApproveStepChildSOADTO> list = recruitingQueryService.getChildItemList(1, resultChildItemList, drvCheckPOMap, 1, 1);
        Assert.assertTrue(!list.isEmpty());
    }

    @Test
    public void queryApproveStepRecord() {
        QueryApproveStepRecordSOARequestType requestType = new QueryApproveStepRecordSOARequestType();
        requestType.setApproveSourceId(1L);
        requestType.setApproveType(1);
        requestType.setRecruitingRecordIds(Arrays.asList(1L));
        List<ApproveStepRecordPO> stepRecordPOS = Lists.newArrayList();
        ApproveStepRecordPO approveStepRecordPO = new ApproveStepRecordPO();
        approveStepRecordPO.setRecruitingType(1);
        approveStepRecordPO.setRecordType(1);
        approveStepRecordPO.setRecruitingId(1L);
        approveStepRecordPO.setRecruitingRecordId(1L);
        approveStepRecordPO.setRecordContent("[{\"attributeKey\":\"approverStatus\",\"changeValue\":\"2\"}]");
        approveStepRecordPO.setModifyUser("11");
        stepRecordPOS.add(approveStepRecordPO);
        Mockito.when(recordRespository.queryList(requestType.getApproveSourceId(), requestType.getApproveType(), requestType.getRecruitingRecordIds())).thenReturn(stepRecordPOS);
        Result<List<QueryApproveStepRecordSOADTO>> result = recruitingQueryService.queryApproveStepRecord(requestType);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void queryRecruitingDrvForPenalty() throws SQLException {
        QueryRecruitingDrvForPenaltyRequestType requestType = new QueryRecruitingDrvForPenaltyRequestType();
        requestType.setDriverId(1L);
        requestType.setDrivermobile("111");
        List<DrvRecruitingPO> list = Lists.newArrayList();
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setApproveAging(1);
        drvRecruitingPO.setApproverStatus(4);
        list.add(drvRecruitingPO);
        Mockito.when(drvRecruitingRepository.queryRecruitingDrvByPhoneORId("111", 1L)).thenReturn(list);
        Result<QueryRecruitingDrvForPenaltyResponseType> result = recruitingQueryService.queryRecruitingDrvForPenalty(requestType);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void queryRecruitingDrvForPenalty1() throws SQLException {
        QueryRecruitingDrvForPenaltyRequestType requestType = new QueryRecruitingDrvForPenaltyRequestType();
        Result<QueryRecruitingDrvForPenaltyResponseType> result = recruitingQueryService.queryRecruitingDrvForPenalty(requestType);
        Assert.assertTrue(result.isSuccess());
    }


    @Test
    public void queryRecruitingDrvForPenalty2() throws SQLException {
        QueryRecruitingDrvForPenaltyRequestType requestType = new QueryRecruitingDrvForPenaltyRequestType();
        requestType.setDriverId(1L);
        requestType.setDrivermobile("111");
        List<DrvRecruitingPO> list = Lists.newArrayList();
        Mockito.when(drvRecruitingRepository.queryRecruitingDrvByPhoneORId("111", 1L)).thenReturn(list);
        Result<QueryRecruitingDrvForPenaltyResponseType> result = recruitingQueryService.queryRecruitingDrvForPenalty(requestType);
        Assert.assertTrue(result.isSuccess());
    }


    @Test
    public void queryRecruitingDrvForPenalty3() throws SQLException {
        QueryRecruitingDrvForPenaltyRequestType requestType = new QueryRecruitingDrvForPenaltyRequestType();
        requestType.setDriverId(1L);
        requestType.setDrivermobile("111");
        List<DrvRecruitingPO> list = Lists.newArrayList();
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setApproveAging(1);
        drvRecruitingPO.setApproverStatus(6);
        list.add(drvRecruitingPO);
        Mockito.when(drvRecruitingRepository.queryRecruitingDrvByPhoneORId("111", 1L)).thenReturn(list);
        Result<QueryRecruitingDrvForPenaltyResponseType> result = recruitingQueryService.queryRecruitingDrvForPenalty(requestType);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void queryRecruitingSOAListTest1() throws Exception {
        Map<String, String> map = Maps.newHashMap();
        map.put("accountId", "30804");
        map.put("accountType", "2");
        SessionHolder.setSessionSource(map);
        RecruitingSOARequestDTO requestDTO = new RecruitingSOARequestDTO();
        Result<PageHolder<RecruitingSOAResponseDTO>> res = recruitingQueryService.queryRecruitingSOAList(requestDTO, new PaginatorDTO(1, 1));
        Assert.assertTrue(res != null);
    }

    @Test
    public void queryRecruitingSOAListTest2() throws Exception {
        Map<String, String> map = Maps.newHashMap();
        map.put("accountId", "30804");
        map.put("accountType", "2");
        SessionHolder.setSessionSource(map);
        RecruitingSOARequestDTO requestDTO = new RecruitingSOARequestDTO();
        Mockito.when(drvRecruitingRepository.findNewDrvRecruitingCount(requestDTO, Lists.newArrayList(),Lists.newArrayList(),2)).thenReturn(1l);
        Result<PageHolder<RecruitingSOAResponseDTO>> res = recruitingQueryService.queryRecruitingSOAList(requestDTO, new PaginatorDTO(1, 1));
        Assert.assertTrue(res != null);
    }

    @Test
    public void queryDrvRecruitingListFromId() throws Exception {
        RecruitingSOARequestDTO recruitingSOARequestDTO = new  RecruitingSOARequestDTO();
        PaginatorDTO paginator = new  PaginatorDTO();
        paginator.setPageNo(1);
        paginator.setPageSize(10);
        List<DrvRecruitingPO>  res = Lists.newArrayList();
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setDrvRecruitingId(1L);
        res.add(drvRecruitingPO);
        Mockito.when(drvRecruitingRepository.findDrvRecruitingIdList(recruitingSOARequestDTO,paginator,Arrays.asList(1),1)).thenReturn(Arrays.asList(1L));
        Mockito.when(drvRecruitingRepository.queryDrvRecruitingListByIds(Arrays.asList(1L))).thenReturn(res);
        List<DrvRecruitingPO> drvRecruitingPOList = recruitingQueryService.queryDrvRecruitingListFromId(recruitingSOARequestDTO,paginator,Arrays.asList(1),1);
        Assert.assertTrue(!drvRecruitingPOList.isEmpty());
    }


}
