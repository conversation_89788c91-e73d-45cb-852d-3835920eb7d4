package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class DriverAccountCommandServiceImplTest {

    @InjectMocks
    private DriverAccountCommandServiceImpl driverAccountCommandService;

    @Mock
    private DrvDrvierRepository drvDrvierRepository;
    @Mock
    private CommonCommandService commonCommandService;
    @Mock
    private DriverPasswordService driverPasswordService;
    @Mock
    private EmailTemplateQconfig emailTemplateQconfig;

    @Mock
    private DriverQueryService driverQueryService;

    String newPwd = "pwd";
    String salt = "123";

    @Test
    public void resetPwd() {
        List<DrvDriverPO> drivers = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvPhone("12344");
        drvDriverPO.setInternalScope(0);
        drvDriverPO.setLoginPwd("aa");
        drivers.add(drvDriverPO);
        Mockito.when(driverQueryService.queryDrvDriverListByAccount(Mockito.anyString())).thenReturn(drivers);

        Mockito.when(driverPasswordService.genResetPwd()).thenReturn(newPwd);
        Mockito.when(driverPasswordService.genPwdSalt()).thenReturn(salt);

        Mockito.when(driverPasswordService.encryptPwd(newPwd, salt)).thenReturn("drivers");
        Mockito.when(drvDrvierRepository.updateDrv(drivers.get(0))).thenReturn(null);

//        Mockito.when(commonCommandService.sendMessageByPhone(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(null);

//        Mockito.when(drvDrvierRepository.queryByPk(Mockito.anyLong())).thenReturn(drivers.get(0));

//        Mockito.when(driverPasswordService.isPasswordValid(Mockito.anyString())).thenReturn(true);
//        Mockito.when(driverPasswordService.isPasswordEqual(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(true);

        Result<DrvDriverPO> result = driverAccountCommandService.resetPwd("123");
        Assert.assertTrue(result.isSuccess() == false);
    }

    @Test
    public void resetPwd2() {
        List<DrvDriverPO> drivers = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvPhone("12344");
        drvDriverPO.setInternalScope(1);
        drvDriverPO.setLoginPwd("aa");
        drvDriverPO.setEmail("<EMAIL>");
        drivers.add(drvDriverPO);
        Mockito.when(driverQueryService.queryDrvDriverListByAccount(Mockito.anyString())).thenReturn(drivers);

        Mockito.when(driverPasswordService.genResetPwd()).thenReturn(newPwd);
        Mockito.when(driverPasswordService.genPwdSalt()).thenReturn(salt);

        Mockito.when(driverPasswordService.encryptPwd(newPwd, salt)).thenReturn("drivers");
        Mockito.when(drvDrvierRepository.updateDrv(drivers.get(0))).thenReturn(null);

//        Mockito.when(commonCommandService.sendMessageByPhone(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(null);


        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().build();
//        Mockito.when(commonCommandService.sendEmail(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(result);

//        Mockito.when(drvDrvierRepository.queryByPk(Mockito.anyLong())).thenReturn(drivers.get(0));

//        Mockito.when(driverPasswordService.isPasswordValid(Mockito.anyString())).thenReturn(true);
//        Mockito.when(driverPasswordService.isPasswordEqual(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(true);

//        Mockito.when(emailTemplateQconfig.getResetPwdEmailTemplate()).thenReturn("1111");
        Result<DrvDriverPO> result1 = driverAccountCommandService.resetPwd("123");
        Assert.assertTrue(!result1.isSuccess());
    }

    @Test
    public void resetPwd3() {
        List<DrvDriverPO> drivers = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvPhone("12344");
        drvDriverPO.setInternalScope(1);
        drvDriverPO.setLoginPwd("aa");
        drvDriverPO.setEmail("<EMAIL>");
        drivers.add(drvDriverPO);
        Mockito.when(driverQueryService.queryDrvDriverListByAccount(Mockito.anyString())).thenReturn(null);

//        Mockito.when(driverPasswordService.genResetPwd()).thenReturn(newPwd);
//        Mockito.when(driverPasswordService.genPwdSalt()).thenReturn(salt);

//        Mockito.when(driverPasswordService.encryptPwd(newPwd, salt)).thenReturn("drivers");
//        Mockito.when(drvDrvierRepository.updateDrv(drivers.get(0))).thenReturn(null);

//        Mockito.when(commonCommandService.sendMessageByPhone(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(null);


        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().build();
//        Mockito.when(commonCommandService.sendEmail(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(result);

//        Mockito.when(drvDrvierRepository.queryByPk(Mockito.anyLong())).thenReturn(drivers.get(0));

//        Mockito.when(driverPasswordService.isPasswordValid(Mockito.anyString())).thenReturn(true);
//        Mockito.when(driverPasswordService.isPasswordEqual(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(true);

        Result<DrvDriverPO> result1 =  driverAccountCommandService.resetPwd("123");
        Assert.assertTrue(result1.isSuccess() == false);
    }

    @Test
    public void resetPwd4() {
        List<DrvDriverPO> drivers = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvPhone("12344");
        drvDriverPO.setInternalScope(1);
        drvDriverPO.setLoginPwd("aa");
        drvDriverPO.setEmail("<EMAIL>");
        drivers.add(drvDriverPO);

        DrvDriverPO drvDriverPO2 = new DrvDriverPO();
        drvDriverPO2.setDrvId(1L);
        drvDriverPO2.setDrvPhone("12344");
        drvDriverPO2.setInternalScope(1);
        drvDriverPO2.setLoginPwd("aa");
        drvDriverPO2.setEmail("<EMAIL>");
        drivers.add(drvDriverPO2);
        Mockito.when(driverQueryService.queryDrvDriverListByAccount(Mockito.anyString())).thenReturn(drivers);

//        Mockito.when(driverPasswordService.genResetPwd()).thenReturn(newPwd);
//        Mockito.when(driverPasswordService.genPwdSalt()).thenReturn(salt);

//        Mockito.when(driverPasswordService.encryptPwd(newPwd, salt)).thenReturn("drivers");
//        Mockito.when(drvDrvierRepository.updateDrv(drivers.get(0))).thenReturn(null);

//        Mockito.when(commonCommandService.sendMessageByPhone(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(null);


        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().build();
//        Mockito.when(commonCommandService.sendEmail(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(result);

//        Mockito.when(drvDrvierRepository.queryByPk(Mockito.anyLong())).thenReturn(drivers.get(0));

//        Mockito.when(driverPasswordService.isPasswordValid(Mockito.anyString())).thenReturn(true);
//        Mockito.when(driverPasswordService.isPasswordEqual(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(true);

        Result<DrvDriverPO> result1 = driverAccountCommandService.resetPwd("123");
        Assert.assertTrue(result1.isSuccess() == false);
    }

    @Test
    public void updatePwd() {
        List<DrvDriverPO> drivers = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvPhone("12344");
        drvDriverPO.setInternalScope(0);
//        drvDriverPO.setLoginPwd("aa");
        drivers.add(drvDriverPO);
//        Mockito.when(drvDrvierRepository.queryByHybridAccount(Mockito.anyString())).thenReturn(drivers);

//        Mockito.when(driverPasswordService.genResetPwd()).thenReturn(newPwd);
        Mockito.when(driverPasswordService.genPwdSalt()).thenReturn(salt);

        Mockito.when(driverPasswordService.encryptPwd(newPwd, salt)).thenReturn("drivers");
        Mockito.when(drvDrvierRepository.updateDrv(drivers.get(0))).thenReturn(null);

//        Mockito.when(commonCommandService.sendMessageByPhone(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(null);

        Mockito.when(drvDrvierRepository.queryByPk(Mockito.anyLong())).thenReturn(drivers.get(0));

        Mockito.when(driverPasswordService.isPasswordValid(Mockito.anyString())).thenReturn(true);
//        Mockito.when(driverPasswordService.isPasswordEqual(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(false);

        Result<DrvDriverPO> result1 =  driverAccountCommandService.updatePwd(1L,"1223",newPwd,newPwd);
        Assert.assertTrue(result1.isSuccess());
    }

    @Test
    public void updatePwd2() {
        List<DrvDriverPO> drivers = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvPhone("12344");
        drvDriverPO.setInternalScope(0);
        drvDriverPO.setLoginPwd("aa");
        drivers.add(drvDriverPO);
//        Mockito.when(drvDrvierRepository.queryByHybridAccount(Mockito.anyString())).thenReturn(drivers);

//        Mockito.when(driverPasswordService.genResetPwd()).thenReturn(newPwd);
//        Mockito.when(driverPasswordService.genPwdSalt()).thenReturn(salt);

//        Mockito.when(driverPasswordService.encryptPwd(newPwd, salt)).thenReturn("drivers");
//        Mockito.when(drvDrvierRepository.updateDrv(drivers.get(0))).thenReturn(null);

//        Mockito.when(commonCommandService.sendMessageByPhone(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(null);

        Mockito.when(drvDrvierRepository.queryByPk(Mockito.anyLong())).thenReturn(drivers.get(0));

        Mockito.when(driverPasswordService.isPasswordValid(Mockito.anyString())).thenReturn(true);
//        Mockito.when(driverPasswordService.isPasswordEqual(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(false);

        Result<DrvDriverPO> result1 =  driverAccountCommandService.updatePwd(1L,"1223",newPwd,newPwd);
        Assert.assertTrue(result1.isSuccess() ==false);
    }

    @Test
    public void updatePwd3() {
        List<DrvDriverPO> drivers = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvPhone("12344");
        drvDriverPO.setInternalScope(0);
        drvDriverPO.setLoginPwd("aa");
        drivers.add(drvDriverPO);
//        Mockito.when(drvDrvierRepository.queryByHybridAccount(Mockito.anyString())).thenReturn(drivers);

//        Mockito.when(driverPasswordService.genResetPwd()).thenReturn(newPwd);
//        Mockito.when(driverPasswordService.genPwdSalt()).thenReturn(salt);

//        Mockito.when(driverPasswordService.encryptPwd(newPwd, salt)).thenReturn("drivers");
//        Mockito.when(drvDrvierRepository.updateDrv(drivers.get(0))).thenReturn(null);

//        Mockito.when(commonCommandService.sendMessageByPhone(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(null);

//        Mockito.when(drvDrvierRepository.queryByPk(Mockito.anyLong())).thenReturn(null);

//        Mockito.when(driverPasswordService.isPasswordValid(Mockito.anyString())).thenReturn(true);
//        Mockito.when(driverPasswordService.isPasswordEqual(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(false);

        Result<DrvDriverPO> result1 = driverAccountCommandService.updatePwd(1L,"1223",newPwd,newPwd);
        Assert.assertTrue(result1.isSuccess() ==false);
    }

    @Test
    public void updatePwd4() {
        List<DrvDriverPO> drivers = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvPhone("12344");
        drvDriverPO.setInternalScope(0);
        drvDriverPO.setLoginPwd("aa");
        drivers.add(drvDriverPO);
//        Mockito.when(drvDrvierRepository.queryByHybridAccount(Mockito.anyString())).thenReturn(drivers);

//        Mockito.when(driverPasswordService.genResetPwd()).thenReturn(newPwd);
//        Mockito.when(driverPasswordService.genPwdSalt()).thenReturn(salt);

//        Mockito.when(driverPasswordService.encryptPwd(newPwd, salt)).thenReturn("drivers");
//        Mockito.when(drvDrvierRepository.updateDrv(drivers.get(0))).thenReturn(null);

//        Mockito.when(commonCommandService.sendMessageByPhone(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(null);

        Mockito.when(drvDrvierRepository.queryByPk(Mockito.anyLong())).thenReturn(drivers.get(0));

        Mockito.when(driverPasswordService.isPasswordValid(Mockito.anyString())).thenReturn(false);
//        Mockito.when(driverPasswordService.isPasswordEqual(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(false);

        Result<DrvDriverPO> result1 =driverAccountCommandService.updatePwd(1L,"1223",newPwd,newPwd);
        Assert.assertTrue(result1.isSuccess() ==false);

    }

    @Test
    public void updatePwd5() {
        List<DrvDriverPO> drivers = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvPhone("12344");
        drvDriverPO.setInternalScope(0);
        drvDriverPO.setLoginPwd("aa");
        drivers.add(drvDriverPO);
//        Mockito.when(drvDrvierRepository.queryByHybridAccount(Mockito.anyString())).thenReturn(drivers);

//        Mockito.when(driverPasswordService.genResetPwd()).thenReturn(newPwd);
//        Mockito.when(driverPasswordService.genPwdSalt()).thenReturn(salt);

//        Mockito.when(driverPasswordService.encryptPwd(newPwd, salt)).thenReturn("drivers");
//        Mockito.when(drvDrvierRepository.updateDrv(drivers.get(0))).thenReturn(null);

//        Mockito.when(commonCommandService.sendMessageByPhone(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(null);

        Mockito.when(drvDrvierRepository.queryByPk(Mockito.anyLong())).thenReturn(drivers.get(0));

        Mockito.when(driverPasswordService.isPasswordValid(Mockito.anyString())).thenReturn(true);
//        Mockito.when(driverPasswordService.isPasswordEqual(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(false);

        Result<DrvDriverPO> result1 = driverAccountCommandService.updatePwd(1L,"1223",newPwd,"1");
        Assert.assertTrue(result1.isSuccess() ==false);

    }
}
