package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.sql.*;
import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class TmsModRecordCommandServiceImplTest {

    @InjectMocks
    private TmsModRecordCommandServiceImpl tmsModRecordCommandService;

    @Mock
    private ChangeRecordAttributeNameQconfig changeRecordAttributeNameQconfig;

    @Mock
    private ModRecordRespository modRecordRespository;
    @Mock
    private TmsApproveStepRecordRespository stepRecordRespository;

    @Before
    public void setUp() throws Exception {
        Mockito.when(changeRecordAttributeNameQconfig.getDriverRecordMap()).thenReturn(Maps.newHashMap());
//        Mockito.when(modRecordRespository.insetModRecord(1L, Lists.newArrayList(),CommonEnum.RecordTypeEnum.DRIVER,CommonEnum.ModTypeEnum.UPDATE,"aa")).thenReturn(null);

    }

    @Test
    public void drvIntendVehicleTypeUpdateModRrd() {
        tmsModRecordCommandService.drvIntendVehicleTypeUpdateModRrd(1L,"ss","ss","aa");
        Assert.assertTrue(true);
    }

    @Test
    public void drvVehRecruitingInsertModRrdList(){
        Mockito.when(changeRecordAttributeNameQconfig.getDrvVehRecruitingRecordMap()).thenReturn(Maps.newHashMap());
//        Mockito.when(modRecordRespository.insetModRecord(1L, Lists.newArrayList(),CommonEnum.RecordTypeEnum.DRIVER,CommonEnum.ModTypeEnum.CREATE,"1")).thenReturn(true);
        Mockito.when(changeRecordAttributeNameQconfig.getCertificateCheckRecordString()).thenReturn("%s%s%s");
        tmsModRecordCommandService.drvVehRecruitingInsertModRrdList(Arrays.asList(1L), CommonEnum.RecordTypeEnum.DRIVER,1,"111",1,1);
        Assert.assertTrue(true);
    }

    @Test
    public void saveRecruitingOperationLog(){
        Mockito.when(changeRecordAttributeNameQconfig.getDrvVehRecruitingRecordMap()).thenReturn(Maps.newHashMap());
//        Mockito.when(modRecordRespository.insetModRecord(1L, Lists.newArrayList(), CommonEnum.RecordTypeEnum.DRIVER,Maps.newHashMap(),"1")).thenReturn(true);
        Result<Boolean> result =  tmsModRecordCommandService.saveRecruitingOperationLog(1L,2,"1",1);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void saveRecruitingSingleRrd(){
        Boolean b = tmsModRecordCommandService.saveRecruitingSingleRrd(1L,"1",2,"");
        Assert.assertTrue(b==null);
    }

    @Test
    public void buildApparoveStepRecordPO(){
        ApproveStepRecordPO recordPO =  tmsModRecordCommandService.buildApparoveStepRecordPO(1L,1,"1",1L,1L,1,1,1,1,2);
        Assert.assertTrue(recordPO!=null);
    }

    @Test
    public void saveSingleAndChildRrd() throws SQLException {
        java.util.List<StepModRecordParams> recordParams = Lists.newArrayList();
        StepModRecordParams stepModRecordParams = new StepModRecordParams();
        stepModRecordParams.setStepId(1L);
        stepModRecordParams.setApproveStatus(1);
        stepModRecordParams.setModifyUser("11");
        recordParams.add(stepModRecordParams);
        List<StepChildModRecordParams > childRecordParams = Lists.newArrayList();
        StepChildModRecordParams stepChildModRecordParams = new StepChildModRecordParams();
        stepChildModRecordParams.setParentStepId(1L);
        stepChildModRecordParams.setStepChildId(1L);
        stepChildModRecordParams.setChildItem(1);
        stepChildModRecordParams.setOrgCheckStatus(1);
        stepChildModRecordParams.setTarCheckStatus(1);
        stepChildModRecordParams.setModifyUser("11");
        stepChildModRecordParams.setCertificateType(1);
        childRecordParams.add(stepChildModRecordParams);
        String modifyUser= "1";
        Long recruitingId = 1L;
        Integer recruitingType = 1;
        ApproveStepRecordPO approveStepRecordPO = new ApproveStepRecordPO();
//        Mockito.when(stepRecordRespository.insert(approveStepRecordPO)).thenReturn(1L);
        Boolean b = tmsModRecordCommandService.saveSingleAndChildRrd(recordParams,childRecordParams,modifyUser,recruitingId,recruitingType);
        Assert.assertTrue(b);
    }

    @Test
    public void drvVehRecruitingInsertModRrdList1() throws SQLException {
        Long l = tmsModRecordCommandService.drvVehRecruitingInsertModRrdList(1L,1,"1","1",1,1);
        Assert.assertTrue(l!=null);
    }

    @Test
    public void insertAutoPassStepRrd() throws SQLException {
        ApproveStepRecordPO approveStepRecordPO = new ApproveStepRecordPO();
//        Mockito.when(stepRecordRespository.insert(approveStepRecordPO)).thenReturn(1L);
        Long long1 = tmsModRecordCommandService.insertAutoPassStepRrd(1L,1,1,1L,1L);
        Assert.assertTrue(long1!=null);
    }

    @Test
    public void insertThirdCertificateRrd() throws SQLException {
        ApproveStepRecordPO approveStepRecordPO = new ApproveStepRecordPO();
//        Mockito.when(stepRecordRespository.insert(approveStepRecordPO)).thenReturn(1L);
        Long long1 = tmsModRecordCommandService.insertThirdCertificateRrd(1L,1,1L,1,1,1,"11");
        Assert.assertTrue(long1!=null);
    }

    @Test
    public void insertAutoTurnDownStepRrd() throws SQLException {
        ApproveStepRecordPO approveStepRecordPO = new ApproveStepRecordPO();
//        Mockito.when(stepRecordRespository.insert(approveStepRecordPO)).thenReturn(1L);
        Long long1 = tmsModRecordCommandService.insertAutoTurnDownStepRrd(1L,1,1,1L);
        Assert.assertTrue(long1!=null);
    }


    @Test
    public void insertThirdCertificateRrd1() throws SQLException {
        ApproveStepRecordPO approveStepRecordPO = new ApproveStepRecordPO();
//        Mockito.when(stepRecordRespository.insert(approveStepRecordPO)).thenReturn(1L);
        Long long1 = tmsModRecordCommandService.insertThirdCertificateRrd(-1L,1,1L,1,2,1,"11");
        Assert.assertTrue(long1!=null);
    }

    @Test
    public void insertRecruitingActiveRrd() throws SQLException {
        Map<String,String> map = Maps.newHashMap();
        Mockito.when(changeRecordAttributeNameQconfig.getDrvVehRecruitingRecordMap()).thenReturn(map);
        Result<Boolean> long1 = tmsModRecordCommandService.insertRecruitingActiveRrd(1L,1,"11");
        Assert.assertTrue(long1!=null);
    }
}