package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.application.query.impl.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class TmsPmsproductQueryServiceTest {

    @InjectMocks
    private TmsPmsproductQueryServiceImpl tmsPmsproductQueryService;
    
    @Test
    public void checkSkuIsExist() {
        Long supplierId = 30804L;
        boolean isExist = tmsPmsproductQueryService.checkSkuIsExist(supplierId);
        Assert.assertTrue(isExist);
    }
}