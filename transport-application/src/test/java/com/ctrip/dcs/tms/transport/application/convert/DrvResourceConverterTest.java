package com.ctrip.dcs.tms.transport.application.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverIdCountSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverIdSOARequestType;
import com.ctrip.dcs.tms.transport.api.resource.driver.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ModRecordConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.DrvDriverHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import org.assertj.core.util.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class DrvResourceConverterTest {

    @InjectMocks
    private DrvResourceConverter drvResourceConverter;
    @Mock
    private ProductionLineUtil productionLineUtil;

    @Before
    public void ready() {
//        Mockito.when(productionLineUtil.getAllIncludeProductionLineList(Lists.newArrayList(1,2,3))).thenReturn(Lists.newArrayList(7));
    }

    @Test
    public void test() {
        DrvDriverPO driverPO = new DrvDriverPO();
        driverPO.setDrvName("詹桑");
        List<DrvBase> drvBases = drvResourceConverter.convertDrvBase(Lists.newArrayList(driverPO));
        Assert.assertTrue(drvBases != null);
        Assert.assertTrue(drvBases.size() != 0);
        Assert.assertTrue(drvBases.size() == 1);
        Assert.assertTrue(drvBases.get(0).getDrvName().equals(driverPO.getDrvName()));
    }

    @Test
    public void convertConditionTest() {
        QueryDriver4BaseSOARequestType req = new QueryDriver4BaseSOARequestType();
        req.setProLineList(Lists.newArrayList(1));
        QueryDrvResourceConditionDTO dto = drvResourceConverter.convertCondition(req);
        Assert.assertTrue(dto != null);
        Assert.assertTrue(dto.getProLineIdList() != null);
        Assert.assertTrue(dto.getProLineIdList().size() == 0);
    }

    @Test
    public void convertConditionTest1() {
        QueryDriverIdCountSOARequestType req = new QueryDriverIdCountSOARequestType();
        req.setCityIdList(Lists.newArrayList(1L, 2L));
        req.setDrvStatusList(Lists.newArrayList(0, 1, 2, 3));
        req.setCountryIdList(Lists.newArrayList(999L, 888L));
        req.setVehicleTypeIdList(Lists.newArrayList(-1L, -2L));
        req.setProLineList(Lists.newArrayList(1, 2, 3));
        QueryDrvResourceConditionDTO dto = drvResourceConverter.convertCondition(req);
        Assert.assertTrue(dto != null);
        Assert.assertTrue(JsonUtil.toJson(req.getCityIdList()).equals(JsonUtil.toJson(dto.getCityIdList())));
        Assert.assertTrue(JsonUtil.toJson(req.getCountryIdList()).equals(JsonUtil.toJson(dto.getCountryIdList())));
        Assert.assertTrue(JsonUtil.toJson(req.getDrvStatusList()).equals(JsonUtil.toJson(dto.getDrvStatusList())));
        Assert.assertTrue(JsonUtil.toJson(req.getVehicleTypeIdList()).equals(JsonUtil.toJson(dto.getVehicleTypeIdList())));
        Assert.assertTrue(!(JsonUtil.toJson(Lists.newArrayList(7L)).equals(JsonUtil.toJson(dto.getProLineIdList()))));
    }

    @Test
    public void convertConditionTest2() {
        QueryDriverIdSOARequestType req = new QueryDriverIdSOARequestType();
        req.setCityIdList(Lists.newArrayList(1L, 2L));
        req.setDrvStatusList(Lists.newArrayList(0, 1, 2, 3));
        req.setCountryIdList(Lists.newArrayList(999L, 888L));
        req.setVehicleTypeIdList(Lists.newArrayList(-1L, -2L));
        req.setProLineList(Lists.newArrayList(1, 2, 3));
        req.setPageSize(200);
        req.setBoundaryDrvId(567L);
        QueryDrvResourceConditionDTO dto = drvResourceConverter.convertCondition(req);
        Assert.assertTrue(dto != null);
        Assert.assertTrue(JsonUtil.toJson(req.getCityIdList()).equals(JsonUtil.toJson(dto.getCityIdList())));
        Assert.assertTrue(JsonUtil.toJson(req.getCountryIdList()).equals(JsonUtil.toJson(dto.getCountryIdList())));
        Assert.assertTrue(JsonUtil.toJson(req.getDrvStatusList()).equals(JsonUtil.toJson(dto.getDrvStatusList())));
        Assert.assertTrue(JsonUtil.toJson(req.getVehicleTypeIdList()).equals(JsonUtil.toJson(dto.getVehicleTypeIdList())));
        Assert.assertTrue(!(JsonUtil.toJson(Lists.newArrayList(7L)).equals(JsonUtil.toJson(dto.getProLineIdList()))));
        Assert.assertTrue(req.getBoundaryDrvId() == dto.getBoundaryDrvId());
        Assert.assertTrue(req.getPageSize() == dto.getPaginator().getPageSize());
        Assert.assertTrue(1 == dto.getPaginator().getPageNo());
    }

    @Test
    public void test1() {
        List<DrvBase> res = drvResourceConverter.convertDrvBase((List<DrvDriverPO>) null);
        Assert.assertTrue(CollectionUtil.isEmpty(res));
    }

    @Test
    public void test2() {
        List<DrvBase> res = drvResourceConverter.convertDrvBase(Lists.newArrayList());
        Assert.assertTrue(CollectionUtil.isEmpty(res));
    }

}