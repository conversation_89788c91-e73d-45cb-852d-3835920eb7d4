package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class DriverLeaveCommandServiceImplTest {

    @InjectMocks
    DriverLeaveCommandServiceImpl service;
    @Mock
    private DrvDriverLeaveRepository drvDriverLeaveRepository;

    @Mock
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;

    @Mock
    private DrvDrvierRepository drvierRepository;

    @Mock
    private DriverQueryService driverQueryService;
    @Mock
    private ApprovalProcessAuthQconfig approvalProcessAuthQconfig;
    @Mock
    CommonCommandService commandService;
    @Mock
    TmsTransportQconfig qconfig;

    @Before
    public void ready() {
        Mockito.when(qconfig.getMaximumNumberYearsOfLeave()).thenReturn(5);
    }

    @Test
    public void closeDrvLeave() {
        DrvDriverLeavePO leavePO = new DrvDriverLeavePO();
        leavePO.setId(1L);
        leavePO.setDrvId(1L);
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setCityId(1L);
        drvDriverPO.setDrvStatus(1);
        Mockito.when(drvierRepository.queryByPk(1L)).thenReturn(drvDriverPO);
        Result<Boolean> disableFlagResult = Result.Builder.<Boolean>newResult().success().build();
        Mockito.when(driverQueryService.judgeDrvOnlinePermission(true, Arrays.asList(1L), Lists.newArrayList())).thenReturn(disableFlagResult);
        Mockito.when(drvDriverLeaveRepository.closeDrvLeave(leavePO)).thenReturn(0);
        Mockito.when(qconfig.getDrvLeaveEpidemicSwitch()).thenReturn(true);
        Result<Boolean>  result = service.closeDrvLeave(leavePO);
        Assert.assertTrue(result.isSuccess() == false);
    }

    @Test
    public void senDriverLeaveSms(){
        DrvDriverLeavePO drvDriverLeavePO = new DrvDriverLeavePO();
        drvDriverLeavePO.setDrvId(1L);
        drvDriverLeavePO.setLeaveBeginTime(DateUtil.getNow());
        drvDriverLeavePO.setLeaveEndTime(DateUtil.getNow());
        drvDriverLeavePO.setLeaveReason("1");
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvName("11");
        drvDriverPO.setDrvPhone("11");
        Map<String,String> params = Maps.newHashMap();
        params.put("DriverName",drvDriverPO.getDrvName());
        params.put("leaveBeginTime",DateUtil.timestampToString(drvDriverLeavePO.getLeaveBeginTime(),DateUtil.YYYYMMDDHHMMSS));
        params.put("leaveEndTime",DateUtil.timestampToString(drvDriverLeavePO.getLeaveEndTime(),DateUtil.YYYYMMDDHHMMSS));
        params.put("leaveReason",drvDriverLeavePO.getLeaveReason());
        Mockito.when(drvierRepository.queryByPk(1L)).thenReturn(drvDriverPO);
        Mockito.when(approvalProcessAuthQconfig.getDriverLeaveSmsCode()).thenReturn("1111");
        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Mockito.when(commandService.sendMessageByPhone(drvDriverPO.getIgtCode(), TmsTransUtil.decrypt(drvDriverPO.getDrvPhone(), KeyType.Phone),"1111",params)).thenReturn(result);
        service.senDriverLeaveSms(drvDriverLeavePO);
        Assert.assertTrue(true);
    }

    @Test
    public void senDriverLeaveSms1(){
        DrvDriverLeavePO drvDriverLeavePO = new DrvDriverLeavePO();
        drvDriverLeavePO.setDrvId(0L);
        drvDriverLeavePO.setLeaveBeginTime(DateUtil.getNow());
        drvDriverLeavePO.setLeaveEndTime(DateUtil.getNow());
        drvDriverLeavePO.setLeaveReason("1");
        service.senDriverLeaveSms(drvDriverLeavePO);
        Assert.assertTrue(true);
    }

    /**
     * 本次请假在未来，且满足不超5年要求
     */
    @Test
    public void testAddDrvLeaveWithNotExistLeave() {
        DrvDriverLeavePO leavePO = new DrvDriverLeavePO();
        leavePO.setDrvId(1L);
        List<DrvLeaveDetailPO> result = new ArrayList();
        Mockito.when(drvDriverLeaveRepository.queryDrvLeaveDetailForDsp(Lists.newArrayList(1L))).thenReturn(result);
        Mockito.when(drvDriverLeaveRepository.addDrvLeave(leavePO)).thenReturn(1);
        Result<Boolean> res = service.addDrvLeave(leavePO);
        Assert.assertTrue(res.isSuccess());
    }

    /**
     * 本次请假在未来，且满足不超5年要求
     */
    @Test
    public void testAddDrvLeaveWithNormal() {
        DrvDriverLeavePO leavePO = new DrvDriverLeavePO();
        leavePO.setDrvId(1L);
        leavePO.setLeaveBeginTime(DateUtil.string2Timestamp("2021-11-03 00:00:00",DateUtil.YYYYMMDDHHMMSS));
        leavePO.setLeaveEndTime(DateUtil.string2Timestamp("2021-11-13 00:00:00",DateUtil.YYYYMMDDHHMMSS));
        List<DrvLeaveDetailPO> result = new ArrayList();
        DrvLeaveDetailPO detailPO = new DrvLeaveDetailPO();// 20天
        detailPO.setLeaveBeginTime(DateUtil.string2Timestamp("2021-04-01 00:00:00",DateUtil.YYYYMMDDHHMMSS));
        detailPO.setLeaveEndTime(DateUtil.string2Timestamp("2021-04-20 00:00:00",DateUtil.YYYYMMDDHHMMSS));
        result.add(detailPO);
        DrvLeaveDetailPO detailPO1 = new DrvLeaveDetailPO(); // 4年11月
        detailPO1.setLeaveBeginTime(DateUtil.string2Timestamp("2020-02-01 00:00:00",DateUtil.YYYYMMDDHHMMSS));
        detailPO1.setLeaveEndTime(DateUtil.string2Timestamp("2025-01-01 00:00:00",DateUtil.YYYYMMDDHHMMSS));
        result.add(detailPO1);
        Mockito.when(drvDriverLeaveRepository.queryDrvLeaveDetailForDsp(Lists.newArrayList(1L))).thenReturn(result);
        Mockito.when(drvDriverLeaveRepository.addDrvLeave(leavePO)).thenReturn(1);
        Result<Boolean> res = service.addDrvLeave(leavePO);
        Assert.assertTrue(res.isSuccess());
    }

    /**
     * 本次请假在请假中，且满足不超5年要求
     */
    @Test
    public void testAddDrvLeaveWithLeaving() {
        DrvDriverLeavePO leavePO = new DrvDriverLeavePO();
        leavePO.setDrvId(1L);
        leavePO.setLeaveBeginTime(DateUtil.string2Timestamp("2021-11-01 00:00:00",DateUtil.YYYYMMDDHHMMSS));
        leavePO.setLeaveEndTime(DateUtil.string2Timestamp("2021-11-11 00:00:00",DateUtil.YYYYMMDDHHMMSS));
        List<DrvLeaveDetailPO> result = new ArrayList();
        DrvLeaveDetailPO detailPO = new DrvLeaveDetailPO();// 20天
        detailPO.setLeaveBeginTime(DateUtil.string2Timestamp("2021-04-01 00:00:00",DateUtil.YYYYMMDDHHMMSS));
        detailPO.setLeaveEndTime(DateUtil.string2Timestamp("2021-04-20 00:00:00",DateUtil.YYYYMMDDHHMMSS));
        result.add(detailPO);
        DrvLeaveDetailPO detailPO1 = new DrvLeaveDetailPO(); // 4年11月
        detailPO1.setLeaveBeginTime(DateUtil.string2Timestamp("2020-02-01 00:00:00",DateUtil.YYYYMMDDHHMMSS));
        detailPO1.setLeaveEndTime(DateUtil.string2Timestamp("2025-01-01 00:00:00",DateUtil.YYYYMMDDHHMMSS));
        result.add(detailPO1);
        Mockito.when(drvDriverLeaveRepository.queryDrvLeaveDetailForDsp(Lists.newArrayList(1L))).thenReturn(result);
        Mockito.when(drvDriverLeaveRepository.addDrvLeave(leavePO)).thenReturn(1);
        Result<Boolean> res = service.addDrvLeave(leavePO);
        Assert.assertTrue(res.isSuccess());
    }
}
