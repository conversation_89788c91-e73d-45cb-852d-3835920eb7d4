package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.application.query.impl.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.DrvDriverLeaveGateway;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class DriverLeaveQueryServiceTest {

    @Mock
    private DrvDriverLeaveRepositoryImpl drvDriverLeaveRepository;

    @Mock
    private DrvDriverLeaveGateway drvDriverLeaveGateway;

    @InjectMocks
    private DriverLeaveQueryServiceImpl driverLeaveQueryService;

    private DrvDriverLeavePO drvDriverLeavePO = new DrvDriverLeavePO();

    private PaginatorDTO paginatorDTO = null;

    @Mock
    TmsTransportQconfig qconfig;

    @Before
    public void before(){
        drvDriverLeavePO.setDrvId(1L);

        DrvLeaveDetailPO drvLeaveDetailPO = new DrvLeaveDetailPO();
        drvLeaveDetailPO.setId(1L);
        Mockito.when(drvDriverLeaveRepository.queryDrvLeaveDetail(drvDriverLeavePO, paginatorDTO)).thenReturn(Lists.newArrayList(drvLeaveDetailPO));

//        Mockito.when(drvDriverLeaveRepository.countDrvLeaveDetail(drvDriverLeavePO)).thenReturn(1);

    }

    @Test
    public void queryDrvLeaveDetail_noPage_test() {
        Result<PageHolder<DrvLeaveDetailPO>> pageHolderResult = driverLeaveQueryService.queryDrvLeaveDetail(drvDriverLeavePO, paginatorDTO);
        Assert.assertTrue(pageHolderResult.isSuccess());
    }

    @Test
    public void queryDrvLeaveDetail_page_test() {
        DrvDriverLeavePO drvDriverLeavePO = new DrvDriverLeavePO();
        drvDriverLeavePO.setDrvId(1L);
        PaginatorDTO paginatorDTO = new PaginatorDTO();
        paginatorDTO.setPageNo(1);
        paginatorDTO.setPageSize(2);
        Result<PageHolder<DrvLeaveDetailPO>> result = driverLeaveQueryService.queryDrvLeaveDetail(drvDriverLeavePO, paginatorDTO);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void queryDrvLeaveDetailForDsp() {
        Result<List<DrvLeaveDetailPO>> result =  driverLeaveQueryService.queryDrvLeaveDetailForDsp(Lists.newArrayList(10000L), false);
        Assert.assertTrue(result.isSuccess());
    }
}
