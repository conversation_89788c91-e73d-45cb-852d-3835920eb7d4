package com.ctrip.dcs.tms.transport.infrastructure.common.util;

import com.ctrip.igt.framework.common.clogging.*;
import org.apache.commons.collections.*;
import org.springframework.util.StringUtils;

import java.text.*;
import java.util.*;


public class StringUtil {
     private final static Logger logger = LoggerFactory.getLogger(StringUtil.class);

    private StringUtil() {

    }

    public static String getParams(Map<String,String> paramsMap) {
        if (MapUtils.isEmpty(paramsMap)) {
            return "";
        }
        StringBuffer params = new StringBuffer();
        for (String key : paramsMap.keySet()) {
            if (params.length() > 1) {
                params.append("&");
            }
            params.append(key).append("=").append(paramsMap.get(key).trim());
        }
        return params.toString();
    }

    public static int getPageCount(int count,int pageSize){
        if((count % pageSize) == 0){
            count = (count / pageSize);
        }else{
            count = (count / pageSize) + 1;
        }
        return count;
    }

    /**
     * 获取两个数百分比的值
     * @param num1
     * @param num2
     * @param retain 保留小数位数
     * @return
     */
    public static String numberFormat(double num1, double num2, int retain) {
        if(num2 <= 0 || num1 <= 0){
            return "0";
        }
        try {
            NumberFormat numberFormat = NumberFormat.getInstance();
            numberFormat.setMaximumFractionDigits(retain);
            numberFormat.setGroupingUsed(false);
            String result = numberFormat.format( num1 /  num2 * 100);
            return result;
        }catch (Exception e){
            logger.error("numberFormat","params=num1:{},num2:{},retain{},e{}:",num1,num2,retain,e);
            return "0";
        }
    }

    /**
     * 生成[min,max]之间的随机整数
     *
     * @param min
     * @param max
     * @return
     */
    public static int randomNum(int min, int max) {
        Random random = new Random();
        int s = random.nextInt(max) % (max - min + 1) + min;
        return s;
    }
    /**
     * 字符串拼接
     * @param strs
     * @return
     */
    public static String concat(String... strs){
        if(strs == null || strs.length == 0){
            return null;
        }
        StringBuilder result = new StringBuilder();
        Arrays.asList(strs).forEach(str->{
            result.append(str);
        });
        return result.toString();
    }

    public static String percentData(double num1, double num2, int retain) {
        if(num2 <= 0){
            return "0";
        }
        try {
            NumberFormat numberFormat = NumberFormat.getInstance();
            numberFormat.setMaximumFractionDigits(retain);
            numberFormat.setGroupingUsed(false);
            String result = numberFormat.format( num1 /  num2 * 100);
            return result;
        }catch (Exception e){
            logger.error("numberFormat","params=num1:{},num2:{},retain{},e{}:",num1,num2,retain,e);
            return "0";
        }
    }

    //生成8位随机数(字母+数字)
    public static String createRandomData(){
        return generateRandomString(8);
    }
    /**
     * 字符串判空
     * @param str
     * @return
     */
    public static boolean isEmpty(String str){
        return StringUtils.isEmpty(str);
    }

    public static String generateRandomString(int length) {
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder randomString = new StringBuilder();
        Random random = new Random();

        // 生成包含字母和数字的随机字符串
        boolean hasLetter = false;
        boolean hasDigit = false;
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(characters.length());
            char c = characters.charAt(index);
            randomString.append(c);
            if (Character.isLetter(c)) {
                hasLetter = true;
            } else if (Character.isDigit(c)) {
                hasDigit = true;
            }
        }

        // 如果生成的随机字符串不包含字母或数字，则重新生成
        if (!hasLetter || !hasDigit) {
            return generateRandomString(length);
        }

        return randomString.toString();
    }
}