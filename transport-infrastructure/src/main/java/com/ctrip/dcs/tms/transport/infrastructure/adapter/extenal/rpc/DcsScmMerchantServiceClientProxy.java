package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc;

import com.ctrip.dcs.scm.merchant.interfaces.*;
import com.ctrip.dcs.scm.merchant.interfaces.message.*;
import com.ctrip.igt.framework.soa.client.*;

/**
 * 供应管理商户服务
 * <AUTHOR>
 * @Date 2020/3/13 16:53
 */
@ServiceClient(value = DcsScmMerchantServiceClient.class,format = "json")
public interface DcsScmMerchantServiceClientProxy {

    /**
     * 查询服务商列表
     */
    QueryServiceProviderListResponseType queryServiceProviderList(QueryServiceProviderListRequestType request) throws Exception;

    /**
     * 查询服务商下服务的城市
     */
    QueryContractListResponseType queryContractList(QueryContractListRequestType request)throws Exception;

    /**
     * 查询供应商信息
     */
    QuerySupplierListResponseType querySupplierList(QuerySupplierListRequestType request);


    /**
     * 查询合同信息
     * @param request
     * @return
     * @throws Exception
     */
    QueryBasicContractResponseType queryBasicContract(QueryBasicContractRequestType request);

    QueryBasicSupplierResponseType queryBasicSupplier(QueryBasicSupplierRequestType request);

}
