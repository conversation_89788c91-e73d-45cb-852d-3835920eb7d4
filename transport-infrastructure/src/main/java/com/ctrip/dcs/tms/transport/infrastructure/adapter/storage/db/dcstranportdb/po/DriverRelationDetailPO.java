package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.annotation.*;

import javax.persistence.*;
import java.sql.*;

/**
 * <AUTHOR>
 * @Date 2020/11/30 16:40
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "")
public class DriverRelationDetailPO implements DalPojo {

    @Column(name = "drv_id")
    @Type(value = Types.BIGINT)
    Long drvId;

    @Column(name = "drv_name")
    @Type(value = Types.VARCHAR)
    String drvName;

    @Column(name = "drv_phone")
    @Type(value = Types.VARCHAR)
    String drvPhone;

    @Column(name = "city_id")
    @Type(value = Types.BIGINT)
    Long cityId;

    @Column(name = "vehicle_type_id")
    @Type(value = Types.BIGINT)
    Long vehicleTypeId;

    @Column(name = "drv_from")
    @Type(value = Types.TINYINT)
    Integer drvFrom;

    @Column(name = "drv_language")
    @Type(value = Types.VARCHAR)
    String drvLanguage;

    /**
     * 司机状态(0未激活、1上线、2冻结、3下线)
     */
    @Column(name = "drv_status")
    @Type(value = Types.TINYINT)
    private Integer drvStatus;

    @Column(name = "supplier_id")
    @Type(value = Types.BIGINT)
    Long supplierId;

    @Column(name = "apply_status")
    @Type(value = Types.INTEGER)
    private Integer applyStatus;
    /**
     * 逻辑产线
     */
    @Column(name = "category_synthesize_code")
    @Type(value = Types.TINYINT)
    private Integer categorySynthesizeCode;

    /**
     * 是否国内。1:国内，2:国际
     */
    @Column(name = "internal_scope")
    @Type(value = Types.TINYINT)
    private Integer internalScope;

    public Integer getInternalScope() {
        return internalScope;
    }

    public void setInternalScope(Integer internalScope) {
        this.internalScope = internalScope;
    }

    public Integer getCategorySynthesizeCode() {
        return categorySynthesizeCode;
    }

    public void setCategorySynthesizeCode(Integer categorySynthesizeCode) {
        this.categorySynthesizeCode = categorySynthesizeCode;
    }

    public Long getDrvId() {
        return drvId;
    }

    public void setDrvId(Long drvId) {
        this.drvId = drvId;
    }

    public String getDrvName() {
        return drvName;
    }

    public void setDrvName(String drvName) {
        this.drvName = drvName;
    }

    public String getDrvPhone() {
        return drvPhone;
    }

    public void setDrvPhone(String drvPhone) {
        this.drvPhone = drvPhone;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public Long getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(Long vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public Integer getDrvFrom() {
        return drvFrom;
    }

    public void setDrvFrom(Integer drvFrom) {
        this.drvFrom = drvFrom;
    }

    public String getDrvLanguage() {
        return drvLanguage;
    }

    public void setDrvLanguage(String drvLanguage) {
        this.drvLanguage = drvLanguage;
    }

    public Integer getDrvStatus() {
        return drvStatus;
    }

    public void setDrvStatus(Integer drvStatus) {
        this.drvStatus = drvStatus;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(Integer applyStatus) {
        this.applyStatus = applyStatus;
    }
}
