package com.ctrip.dcs.tms.transport.infrastructure.common.config;

import org.springframework.beans.factory.annotation.*;
import org.springframework.context.annotation.*;
import qunar.tc.qmq.*;
import qunar.tc.qmq.dal.*;
import qunar.tc.qmq.producer.*;

/**
 * QMQ配置
 * <AUTHOR>
 * @Date 2020/2/27 19:53
 */
@Configuration
public class QmqProducerConfig {

    @Value("${tms.transport.db.name}")
    private String DA_NAME;

    @Bean
    MessageProducer messageProducer() {
        MessageProducerProvider producer = new MessageProducerProvider();
        DalTransactionProvider dalTransactionProvider = new DalTransactionProvider(DA_NAME);
        producer.setTransactionProvider(dalTransactionProvider);
        producer.init();
        return producer;
    }

}
