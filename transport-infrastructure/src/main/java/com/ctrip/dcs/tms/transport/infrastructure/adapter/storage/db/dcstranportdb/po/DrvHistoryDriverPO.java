package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.math.BigInteger;
import java.sql.Date;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2022-11-04
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "drv_history_driver")
public class DrvHistoryDriverPO implements DalPojo {

    /**
     * 司机id
     */
    @Id
    @Column(name = "drv_id")
    @Type(value = Types.BIGINT)
    private Long drvId;

    /**
     * 司机姓名
     */
    @Column(name = "drv_name")
    @Type(value = Types.VARCHAR)
    private String drvName;

    /**
     * 租赁公司
     */
    @Column(name = "corp_id")
    @Type(value = Types.BIGINT)
    private Long corpId;

    /**
     * 租赁公司名称(冗余)
     */
    @Column(name = "corp_name")
    @Type(value = Types.VARCHAR)
    private String corpName;

    /**
     * 车辆id - Q侧
     */
    @Column(name = "car_id")
    @Type(value = Types.BIGINT)
    private Long carId;

    /**
     * 车牌号(冗余)
     */
    @Column(name = "car_license")
    @Type(value = Types.VARCHAR)
    private String carLicense;

    /**
     * 手机号 C加密
     */
    @Column(name = "drv_phone")
    @Type(value = Types.VARCHAR)
    private String drvPhone;

    /**
     * 身份证号 C加密
     */
    @Column(name = "drv_idcard")
    @Type(value = Types.VARCHAR)
    private String drvIdcard;

    /**
     * 驾驶证号 C加密
     */
    @Column(name = "drv_license")
    @Type(value = Types.VARCHAR)
    private String drvLicense;

    /**
     * 合同方式 1：租赁公司分成合作，2：保单合作，3：挂靠合作，4：租车合作，5：库管合作，6：顺风保单，7：超级保单
     */
    @Column(name = "coop_mode")
    @Type(value = Types.TINYINT)
    private Integer coopMode;

    /**
     * 运营资质 0：无，1：有
     */
    @Column(name = "opt_qualif")
    @Type(value = Types.TINYINT)
    private Integer optQualif;

    /**
     * 所属城市编码 - Q侧
     */
    @Column(name = "city_code")
    @Type(value = Types.VARCHAR)
    private String cityCode;

    /**
     * 所属城市名称(冗余)
     */
    @Column(name = "city_name")
    @Type(value = Types.VARCHAR)
    private String cityName;

    /**
     * 销售员
     */
    @Column(name = "salesman")
    @Type(value = Types.VARCHAR)
    private String salesman;

    /**
     * 司机照片
     */
    @Column(name = "pic_url")
    @Type(value = Types.VARCHAR)
    private String picUrl;

    /**
     * 住址
     */
    @Column(name = "drv_addr")
    @Type(value = Types.VARCHAR)
    private String drvAddr;

    /**
     * 备注
     */
    @Column(name = "comments")
    @Type(value = Types.VARCHAR)
    private String comments;

    /**
     * 初次领证日期
     */
    @Column(name = "certi_date")
    @Type(value = Types.DATE)
    private Date certiDate;

    /**
     * 开户行名称
     */
    @Column(name = "bank_name")
    @Type(value = Types.VARCHAR)
    private String bankName;

    /**
     * 银行账号
     */
    @Column(name = "bank_accout")
    @Type(value = Types.VARCHAR)
    private String bankAccout;

    /**
     * PPM账户
     */
    @Column(name = "ppm_accout")
    @Type(value = Types.VARCHAR)
    private String ppmAccout;

    /**
     * Qunar账户
     */
    @Column(name = "qunar_accout")
    @Type(value = Types.VARCHAR)
    private String qunarAccout;

    /**
     * 冻结小时
     */
    @Column(name = "freeze_hour")
    @Type(value = Types.INTEGER)
    private Integer freezeHour;

    /**
     * 冻结原因
     */
    @Column(name = "freeze_reason")
    @Type(value = Types.VARCHAR)
    private String freezeReason;

    /**
     * 冻结时间
     */
    @Column(name = "freeze_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp freezeTime;

    /**
     * 0：未激活 1：已上线 2：冻结 3：已下线
     */
    @Column(name = "drv_status")
    @Type(value = Types.TINYINT)
    private Integer drvStatus;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 司机来源
     */
    @Column(name = "drv_from")
    @Type(value = Types.TINYINT)
    private Integer drvFrom;

    /**
     * 身份证图片地址
     */
    @Column(name = "idcard_url")
    @Type(value = Types.VARCHAR)
    private String idcardUrl;

    /**
     * 驾驶证图片地址
     */
    @Column(name = "drvcard_url")
    @Type(value = Types.VARCHAR)
    private String drvcardUrl;

    /**
     * 销售员Id
     */
    @Column(name = "salesman_id")
    @Type(value = Types.VARCHAR)
    private String salesmanId;

    /**
     * 驾驶证有效期限开始时间
     */
    @Column(name = "expiry_begin_date")
    @Type(value = Types.DATE)
    private Date expiryBeginDate;

    /**
     * 驾驶证有效期限结束时间
     */
    @Column(name = "expiry_end_date")
    @Type(value = Types.DATE)
    private Date expiryEndDate;

    /**
     * 操作来源 1：供应商，2：BD
     */
    @Column(name = "op_from")
    @Type(value = Types.TINYINT)
    private Integer opFrom;

    /**
     * 操作类型 0：新增，1：更新，2：删除
     */
    @Column(name = "op_type")
    @Type(value = Types.TINYINT)
    private Integer opType;

    /**
     * 操作人id
     */
    @Column(name = "op_user_id")
    @Type(value = Types.INTEGER)
    private Integer opUserId;

    /**
     * 归属 1：直营，2：代理
     */
    @Column(name = "affiliation")
    @Type(value = Types.TINYINT)
    private Integer affiliation;

    /**
     * 邀请码（用户立即单司机招募活动）
     */
    @Column(name = "invite_code")
    @Type(value = Types.VARCHAR)
    private String inviteCode;

    /**
     * 是否国内 1：国内，2：国际
     */
    @Column(name = "internal_scope")
    @Type(value = Types.TINYINT)
    private Integer internalScope;

    /**
     * 司机用户名（在所有司机中唯一的用户名）
     */
    @Column(name = "drv_unique_name")
    @Type(value = Types.VARCHAR)
    private String drvUniqueName;

    /**
     * 密码hash值
     */
    @Column(name = "pwd_hash")
    @Type(value = Types.VARCHAR)
    private String pwdHash;

    /**
     * 盐，随机产生的字符串，生成密码hash时、校验密码时需使用
     */
    @Column(name = "salt")
    @Type(value = Types.VARCHAR)
    private String salt;

    /**
     * 微信号
     */
    @Column(name = "wechat")
    @Type(value = Types.VARCHAR)
    private String wechat;

    /**
     * 司机邮箱地址，如重置密码时发送邮箱到此邮箱
     */
    @Column(name = "email")
    @Type(value = Types.VARCHAR)
    private String email;

    /**
     * 国家代码（三字码，如中国是CHN）
     */
    @Column(name = "country_id")
    @Type(value = Types.VARCHAR)
    private String countryId;

    /**
     * 国家名 - Q侧
     */
    @Column(name = "country_name")
    @Type(value = Types.VARCHAR)
    private String countryName;

    /**
     * 国际区号，如中国国际区号是 86
     */
    @Column(name = "phone_area_code")
    @Type(value = Types.VARCHAR)
    private String phoneAreaCode;

    /**
     * C侧im账号uid
     */
    @Column(name = "ctrip_account")
    @Type(value = Types.VARCHAR)
    private String ctripAccount;

    /**
     * 证件号码
     */
    @Column(name = "certificate_number")
    @Type(value = Types.VARCHAR)
    private String certificateNumber;

    /**
     * 审批类型 3：新增/审核通过，2：审核中，4：审核失败
     */
    @Column(name = "approve_status")
    @Type(value = Types.TINYINT)
    private Integer approveStatus;

    /**
     * 准驾车型
     */
    @Column(name = "quasi_driving_type")
    @Type(value = Types.VARCHAR)
    private String quasiDrivingType;

    /**
     * 首次上线时间（未激活变为已上线的时间）
     */
    @Column(name = "first_online_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp firstOnlineTime;

    /**
     * 出生日期
     */
    @Column(name = "birth_time")
    @Type(value = Types.DATE)
    private Date birthTime;

    /**
     * 0 男 1 女
     */
    @Column(name = "sex")
    @Type(value = Types.INTEGER)
    private Integer sex;

    /**
     * 活体人脸核身结果id
     */
    @Column(name = "liveness_recognition_id")
    @Type(value = Types.INTEGER)
    private Integer livenessRecognitionId;

    /**
     * 照片人脸核身结果id
     */
    @Column(name = "image_recognition_id")
    @Type(value = Types.INTEGER)
    private Integer imageRecognitionId;

    /**
     * 语言id
     */
    @Column(name = "language_ids")
    @Type(value = Types.VARCHAR)
    private String languageIds;

    /**
     * 语言名称
     */
    @Column(name = "language_names")
    @Type(value = Types.VARCHAR)
    private String languageNames;

    /**
     * 是否完成迁移(0没有，1完成)
     */
    @Column(name = "data_migrated")
    @Type(value = Types.INTEGER)
    private Integer dataMigrated;

    /**
     * 手机区号
     */
    @Column(name = "drv_phone_code")
    @Type(value = Types.CHAR)
    private String drvPhoneCode;

    /**
     * 冻结时间
     */
    @Column(name = "create_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp createTime;

    /**
     * 冻结时间
     */
    @Column(name = "update_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp updateTime;

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public Long getDrvId() {
        return drvId;
    }

    public void setDrvId(Long drvId) {
        this.drvId = drvId;
    }

    public String getDrvName() {
        return drvName;
    }

    public void setDrvName(String drvName) {
        this.drvName = drvName;
    }

    public Long getCorpId() {
        return corpId;
    }

    public void setCorpId(Long corpId) {
        this.corpId = corpId;
    }

    public String getCorpName() {
        return corpName;
    }

    public void setCorpName(String corpName) {
        this.corpName = corpName;
    }

    public Long getCarId() {
        return carId;
    }

    public void setCarId(Long carId) {
        this.carId = carId;
    }

    public String getCarLicense() {
        return carLicense;
    }

    public void setCarLicense(String carLicense) {
        this.carLicense = carLicense;
    }

    public String getDrvPhone() {
        return drvPhone;
    }

    public void setDrvPhone(String drvPhone) {
        this.drvPhone = drvPhone;
    }

    public String getDrvIdcard() {
        return drvIdcard;
    }

    public void setDrvIdcard(String drvIdcard) {
        this.drvIdcard = drvIdcard;
    }

    public String getDrvLicense() {
        return drvLicense;
    }

    public void setDrvLicense(String drvLicense) {
        this.drvLicense = drvLicense;
    }

    public Integer getCoopMode() {
        return coopMode;
    }

    public void setCoopMode(Integer coopMode) {
        this.coopMode = coopMode;
    }

    public Integer getOptQualif() {
        return optQualif;
    }

    public void setOptQualif(Integer optQualif) {
        this.optQualif = optQualif;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getSalesman() {
        return salesman;
    }

    public void setSalesman(String salesman) {
        this.salesman = salesman;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getDrvAddr() {
        return drvAddr;
    }

    public void setDrvAddr(String drvAddr) {
        this.drvAddr = drvAddr;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Date getCertiDate() {
        return certiDate;
    }

    public void setCertiDate(Date certiDate) {
        this.certiDate = certiDate;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAccout() {
        return bankAccout;
    }

    public void setBankAccout(String bankAccout) {
        this.bankAccout = bankAccout;
    }

    public String getPpmAccout() {
        return ppmAccout;
    }

    public void setPpmAccout(String ppmAccout) {
        this.ppmAccout = ppmAccout;
    }

    public String getQunarAccout() {
        return qunarAccout;
    }

    public void setQunarAccout(String qunarAccout) {
        this.qunarAccout = qunarAccout;
    }

    public Integer getFreezeHour() {
        return freezeHour;
    }

    public void setFreezeHour(Integer freezeHour) {
        this.freezeHour = freezeHour;
    }

    public String getFreezeReason() {
        return freezeReason;
    }

    public void setFreezeReason(String freezeReason) {
        this.freezeReason = freezeReason;
    }

    public Timestamp getFreezeTime() {
        return freezeTime;
    }

    public void setFreezeTime(Timestamp freezeTime) {
        this.freezeTime = freezeTime;
    }

    public Integer getDrvStatus() {
        return drvStatus;
    }

    public void setDrvStatus(Integer drvStatus) {
        this.drvStatus = drvStatus;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public Integer getDrvFrom() {
        return drvFrom;
    }

    public void setDrvFrom(Integer drvFrom) {
        this.drvFrom = drvFrom;
    }

    public String getIdcardUrl() {
        return idcardUrl;
    }

    public void setIdcardUrl(String idcardUrl) {
        this.idcardUrl = idcardUrl;
    }

    public String getDrvcardUrl() {
        return drvcardUrl;
    }

    public void setDrvcardUrl(String drvcardUrl) {
        this.drvcardUrl = drvcardUrl;
    }

    public String getSalesmanId() {
        return salesmanId;
    }

    public void setSalesmanId(String salesmanId) {
        this.salesmanId = salesmanId;
    }

    public Date getExpiryBeginDate() {
        return expiryBeginDate;
    }

    public void setExpiryBeginDate(Date expiryBeginDate) {
        this.expiryBeginDate = expiryBeginDate;
    }

    public Date getExpiryEndDate() {
        return expiryEndDate;
    }

    public void setExpiryEndDate(Date expiryEndDate) {
        this.expiryEndDate = expiryEndDate;
    }

    public Integer getOpFrom() {
        return opFrom;
    }

    public void setOpFrom(Integer opFrom) {
        this.opFrom = opFrom;
    }

    public Integer getOpType() {
        return opType;
    }

    public void setOpType(Integer opType) {
        this.opType = opType;
    }

    public Integer getOpUserId() {
        return opUserId;
    }

    public void setOpUserId(Integer opUserId) {
        this.opUserId = opUserId;
    }

    public Integer getAffiliation() {
        return affiliation;
    }

    public void setAffiliation(Integer affiliation) {
        this.affiliation = affiliation;
    }

    public String getInviteCode() {
        return inviteCode;
    }

    public void setInviteCode(String inviteCode) {
        this.inviteCode = inviteCode;
    }

    public Integer getInternalScope() {
        return internalScope;
    }

    public void setInternalScope(Integer internalScope) {
        this.internalScope = internalScope;
    }

    public String getDrvUniqueName() {
        return drvUniqueName;
    }

    public void setDrvUniqueName(String drvUniqueName) {
        this.drvUniqueName = drvUniqueName;
    }

    public String getPwdHash() {
        return pwdHash;
    }

    public void setPwdHash(String pwdHash) {
        this.pwdHash = pwdHash;
    }

    public String getSalt() {
        return salt;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCountryId() {
        return countryId;
    }

    public void setCountryId(String countryId) {
        this.countryId = countryId;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public String getPhoneAreaCode() {
        return phoneAreaCode;
    }

    public void setPhoneAreaCode(String phoneAreaCode) {
        this.phoneAreaCode = phoneAreaCode;
    }

    public String getCtripAccount() {
        return ctripAccount;
    }

    public void setCtripAccount(String ctripAccount) {
        this.ctripAccount = ctripAccount;
    }

    public String getCertificateNumber() {
        return certificateNumber;
    }

    public void setCertificateNumber(String certificateNumber) {
        this.certificateNumber = certificateNumber;
    }

    public Integer getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getQuasiDrivingType() {
        return quasiDrivingType;
    }

    public void setQuasiDrivingType(String quasiDrivingType) {
        this.quasiDrivingType = quasiDrivingType;
    }

    public Timestamp getFirstOnlineTime() {
        return firstOnlineTime;
    }

    public void setFirstOnlineTime(Timestamp firstOnlineTime) {
        this.firstOnlineTime = firstOnlineTime;
    }

    public Date getBirthTime() {
        return birthTime;
    }

    public void setBirthTime(Date birthTime) {
        this.birthTime = birthTime;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public Integer getLivenessRecognitionId() {
        return livenessRecognitionId;
    }

    public void setLivenessRecognitionId(Integer livenessRecognitionId) {
        this.livenessRecognitionId = livenessRecognitionId;
    }

    public Integer getImageRecognitionId() {
        return imageRecognitionId;
    }

    public void setImageRecognitionId(Integer imageRecognitionId) {
        this.imageRecognitionId = imageRecognitionId;
    }

    public String getLanguageIds() {
        return languageIds;
    }

    public void setLanguageIds(String languageIds) {
        this.languageIds = languageIds;
    }

    public String getLanguageNames() {
        return languageNames;
    }

    public void setLanguageNames(String languageNames) {
        this.languageNames = languageNames;
    }

    public Integer getDataMigrated() {
        return dataMigrated;
    }

    public void setDataMigrated(Integer dataMigrated) {
        this.dataMigrated = dataMigrated;
    }

    public String getDrvPhoneCode() {
        return drvPhoneCode;
    }

    public void setDrvPhoneCode(String drvPhoneCode) {
        this.drvPhoneCode = drvPhoneCode;
    }

}