package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.DriverGuideDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.VehVehicleDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverGuideProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverGuidQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverGuidSearchService;
import com.ctrip.dcs.tms.transport.infrastructure.common.converter.DriverGuideConverter;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverGuidDriverRequestDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverGuidVehicleRequestDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.frt.product.soa.DriverInfoResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.tour.driver.platform.api.contract.SearchVehicleResponseType;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.EVENT_SPLIT;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.FILTER_BY_SUPPLIER_ID;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.QUERY_DRIVER;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventType.BRIDGE;

@Component
public class DriverGuideProxyImpl implements DriverGuideProxy {

  private static final Logger logger = LoggerFactory.getLogger(DriverGuideProxyImpl.class);
  private static final String FORBIDDEN_GET_VEHICLE_INFO_WITH_NO_VEHICLE_ID = "forbidden_get_vehicle_info_with_no_vehicle_id";


  @Autowired
  DriverGuidQueryService driverGuidQueryService;
  @Autowired
  DriverGuidSearchService driverGuidSearchService;

  @Autowired
  TmsTransportQconfig transportQconfig;

  private static String IN_GRAY_CONTROL = "T";
  private static Long ALL = -1L;

  @Autowired
  DriverGuideConverter driverGuideConverter;

  @Override
  public List<DriverGuideDTO> getDriverInfo(DriverGuidDriverRequestDTO request) {
    return filterDriverBySupplierList(getDriverInfoByPartition(request));
  }

  //判断是否在灰度控制范围内
  protected List<DriverGuideDTO> filterDriverBySupplierList(List<DriverGuideDTO> driverInfoByPartition) {
    // 如果包含-1，则全放开
    if (transportQconfig.getGraySupplierList().contains(ALL)) {
      return driverInfoByPartition;
    }
    // 判断是否全在灰度控制范围内（空也直接返回）
    if(Optional.ofNullable(driverInfoByPartition).orElse(Lists.newArrayList()).stream().allMatch(driverGuideDTO -> transportQconfig.getGraySupplierList().contains(driverGuideDTO.getSupplierId()))) {
      return driverInfoByPartition;
    }
    // 不在范围内则直接返回
    logger.info(BRIDGE, QUERY_DRIVER + EVENT_SPLIT + FILTER_BY_SUPPLIER_ID);
    return Lists.newArrayList();
  }

  protected List<DriverGuideDTO> getDriverInfoByPartition(DriverGuidDriverRequestDTO request) {
    List<Long> driverIdList = request.getDriverIdList();
    List<String> drvPhoneList = request.getDriverPhoneList();
    List<DriverGuideDTO> resultList = Lists.newArrayList();

    if (CollectionUtils.isNotEmpty(driverIdList)) {
      driverIdList = driverIdList.stream().distinct().collect(Collectors.toList());
      List<List<Long>> partition =
        Lists.partition(driverIdList, transportQconfig.getQueryDriverGuidePageSize());
      for(List<Long> driverIdSubList : partition) {
        request.setDriverIdList(driverIdSubList);
        DriverInfoResponseType driverInfo = driverGuidQueryService.getDriverInfo(driverGuideConverter.buildDriverInfoRequestType(request));
        resultList.addAll(driverGuideConverter.convert(driverInfo, transportQconfig.getDefaultDriverPic()));
      }
    }

    if (CollectionUtils.isNotEmpty(drvPhoneList)) {
      drvPhoneList = drvPhoneList.stream().distinct().collect(Collectors.toList());
      List<List<String>> partition =
        Lists.partition(drvPhoneList, transportQconfig.getQueryDriverGuidePageSize());
      for(List<String> drvPhoneSubList : partition) {
        request.setDriverPhoneList(drvPhoneSubList);
        DriverInfoResponseType driverInfo = driverGuidQueryService.getDriverInfo(driverGuideConverter.buildDriverInfoRequestType(request));
        resultList.addAll(driverGuideConverter.convert(driverInfo, transportQconfig.getDefaultDriverPic()));
      }
    }
    return resultList;
 }

  @Override
  public List<VehVehicleDTO> getVehicleInfo(DriverGuidVehicleRequestDTO request) {
    if (CollectionUtils.isEmpty(request.getVehicleIdList()) && CollectionUtils.isEmpty(request.getVehicleLicenseList())) {
      Cat.logEvent(BRIDGE, FORBIDDEN_GET_VEHICLE_INFO_WITH_NO_VEHICLE_ID);
      logger.info(FORBIDDEN_GET_VEHICLE_INFO_WITH_NO_VEHICLE_ID,
        "getVehicleInfo with no vehicleId & no vehicle license {} : ", request);
      return Collections.emptyList();
    }

    return filterBySupplierList(getVehicleListByPartition(request));
  }

  private List<VehVehicleDTO> filterBySupplierList(List<VehVehicleDTO> vehicleListByPartition) {
    // 如果包含-1，则全放开
    if (transportQconfig.getGraySupplierList().contains(ALL)) {
      return vehicleListByPartition;
    }
    // 判断是否全在灰度控制范围内（空也直接返回）
    if(Optional.ofNullable(vehicleListByPartition).orElse(Lists.newArrayList()).stream().allMatch(vehicleDTO -> transportQconfig.getGraySupplierList().contains(vehicleDTO.getSupplierId()))) {
      return vehicleListByPartition;
    }
    // 不在范围内则直接返回
    return Lists.newArrayList();
  }

  protected List<VehVehicleDTO> getVehicleListByPartition(DriverGuidVehicleRequestDTO request) {
    List<VehVehicleDTO> resultList = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(request.getVehicleIdList())) {
      request.setVehicleIdList(request.getVehicleIdList().stream().distinct().collect(Collectors.toList()));
      List<List<Long>> partition =
        Lists.partition(request.getVehicleIdList(), transportQconfig.getQueryDriverGuidePageSize());
      for (List<Long> vehicleIdSubList : partition) {
        request.setVehicleIdList(vehicleIdSubList);
        resultList.addAll(driverGuideConverter.convertVehicleList(driverGuidQueryService.getVehicleInfo(
          driverGuideConverter.buildVehicleInfoRequestType4GetVehicleInfo(request))));
      }
    }

    if (CollectionUtils.isNotEmpty(request.getVehicleLicenseList())) {
      request.setVehicleLicenseList(request.getVehicleLicenseList().stream().distinct().collect(Collectors.toList()));
      List<List<String>> partition =
        Lists.partition(request.getVehicleLicenseList(), transportQconfig.getQueryDriverGuidePageSize());
      for (List<String> vehicleLicenseSubList : partition) {
        request.setVehicleLicenseList(vehicleLicenseSubList);
        resultList.addAll(driverGuideConverter.convertVehicleList(driverGuidQueryService.getVehicleInfo(
          driverGuideConverter.buildVehicleInfoRequestType4GetVehicleInfo(request))));
      }
    }
    return resultList;
  }

//  @Override
//  public List<DriverGuideDTO> searchDriver(DriverGuidDriverRequestDTO request) {
//    int pageNo = 1;
//    int pageSize = transportQconfig.getSearchDriverGuidePageSize();
//    List<DriverGuideDTO> driverGuidDTOList = Lists.newArrayList();
//    SearchDriverResponseType responseType;
//    do {
//      responseType =
//        driverGuidSearchService.searchDriver(driverGuideConverter.build2SearchDriverRequestType(request, pageNo, pageSize));
//      driverGuidDTOList.addAll(driverGuideConverter.convert2SearchDriverResponseType(responseType));
//      pageNo++;
//    }while (CollectionUtils.isNotEmpty(responseType.getDriverList()) && responseType.getDriverList().size() == pageSize);
//    return driverGuidDTOList;
//  }

  @Override
  public List<VehVehicleDTO> searchVehicle(DriverGuidVehicleRequestDTO request) {
    if (!isSupplierInGrayControl(request.getSupplierId())) {
      return Lists.newArrayList();
    }
    int pageNo = 1;
    int pageSize = transportQconfig.getSearchDriverGuidePageSize();
    SearchVehicleResponseType searchVehicleResponseType;
    List<VehVehicleDTO> vehVehicleDTOList = Lists.newArrayList();
    do {
      searchVehicleResponseType = driverGuidSearchService.searchVehicle(driverGuideConverter.buildVehicleInfoRequestType(request, pageNo, pageSize));
      vehVehicleDTOList.addAll(driverGuideConverter.convert2VehVehicleDTO(searchVehicleResponseType));
      pageNo++;
  } while (CollectionUtils.isNotEmpty(searchVehicleResponseType.getVehicleList()) && searchVehicleResponseType.getVehicleList().size() == pageSize);
    return vehVehicleDTOList;
  }

  @Override
  public List<Long> queryDriverIdBySupplier(DriverGuidDriverRequestDTO request) {
    if (!isSupplierInGrayControl(request.getSupplierId())) {
      return Lists.newArrayList();
    }
    return Optional.ofNullable(driverGuidSearchService.queryDriverIdBySupplier(driverGuideConverter.convert2QueryDriverIdBySupplierRequestType(request)).getDriverIdList()).orElse(Collections.emptyList());
  }

  @Override
  public boolean isSupplierInGrayControl(Long supplierId) {
    return transportQconfig.getGraySupplierList().contains(ALL) || transportQconfig.getGraySupplierList().contains(supplierId);
  }


  @Override
  public boolean getGrayControl(Long supplierId) {
    return transportQconfig.openSupplierDayProLineMigration() && Objects.equals(IN_GRAY_CONTROL, driverGuidSearchService.getGrayControl(driverGuideConverter.buildGrayControlRequestType(supplierId)).getGrayControl());
  }

}
