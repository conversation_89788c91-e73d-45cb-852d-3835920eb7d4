package com.ctrip.dcs.tms.transport.infrastructure.common.monitoring;

import com.ctrip.dcs.tms.transport.infrastructure.common.util.PlatformUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.metrics.*;
import com.google.common.base.Strings;
import com.google.common.collect.*;
import io.dropwizard.metrics5.*;

import java.util.*;

/**
 * <AUTHOR>
 * 2020-12-11 22:00:11
 * 运力监控指标
 */
public class TransportMetric {

    private static MetricRegistry metricRegistry = MetricsFactory.getMetricRegistry();

    /**
     * 司机缓存相关监控
     */
    private static final String currentLimitingRequest = "tms.transport.current.limiting.request";
    private static final String cacheTimeConsuming = "tms.transport.driver.cache.time.consuming";
    private static final String cacheTimeHit = "tms.transport.driver.cache.time.hit";
    private static final String cacheTimeMiss = "tms.transport.driver.cache.time.miss";
    private static final String hasResult = "tms.transport.query.driver.has.result";
    private static final String noResult = "tms.transport.query.driver.no.result";
    private static final String consistencyError = "tms.transport.query.driver.consistency.deal.error";
    private static final String distributedLockFail = "tms.transport.query.driver.distributed.lock.fail";
    private static final String distributedLockError = "tms.transport.query.driver.distributed.lock.error";
    private static final String distributedLockFault = "tms.transport.query.driver.distributed.lock.fault";
    private static final String cacheTimeSKDrvNameMiss = "tms.transport.driver.cache.drv.name.time.miss";
    private static final String cacheTimeSKDrvPhoneMiss = "tms.transport.driver.cache.drv.phone.time.miss";
    private static final String cacheTimeSkTransportMiss = "tms.transport.driver.cache.drv.transport.time.miss";
    private static final String cacheProtectRefused  = "tms.transport.driver.cache.protect.refused";
    private static final String drvCacheReqFocusDriver  = "tms.transport.driver.cache.focus.driver";
    private static final String drvCacheReqFocusTransport  = "tms.transport.driver.cache.focus.transport";
    private static final String drvCacheReqDBNoResult = "tms.transport.driver.req.db.no.result";
    private static final String drvCacheError = "tms.transport.driver.cache.error";
    private static final String drvCacheDriverMiss = "tms.transport.driver.cache.drv.miss";
    private static final String drvCacheVehicleMiss = "tms.transport.driver.cache.veh.miss";
    private static final String drvCacheDriverHit = "tms.transport.driver.cache.drv.hit";
    private static final String drvCacheVehicleHit = "tms.transport.driver.cache.veh.hit";
    private static final String drvCacheChallengeFailure = "tms.transport.driver.challenge.failure";
    private static final String transportTRocksError = "tms.transport.module.trocks.error";
    private static final String transportResourceReqRecord = "tms.transport.resource.req.record.";

    private static final String queryPointAreaInfoErrorReqRecord = "tms.transport.query.point.area.info.error";

    private static final String queryTransportGroupTakeOrderTimeScopeErrorRecord = "tms.transport.query.transport.group.take.order.time.scope.error";

    private static final String queryDrvDriverListByAccountResDiffRecord = "tms.transport.query.drv.by.hybrid.account.sql.diff.error";

    /**
     * 司机疫情防控相关监控
     */
    private static final String epidemicControlError = "tms.transport.driver.epidemic.control.error.count";
    private static final String epidemicControlNeedReview = "tms.transport.driver.epidemic.control.need.review";
    private static final String epidemicControlCheckPass = "tms.transport.driver.epidemic.control.check.pass";
    private static final String epidemicControlCheckFail = "tms.transport.driver.epidemic.control.check.fail";
    private static final String epidemicControlAll = "tms.transport.driver.epidemic.control.all.count";

    private static final String transportDrvRelationAllTimeConsuming = "tms.transport.driver.relation.all.time.consuming";

    private static final String transportDrvRelationOneTimeConsuming = "tms.transport.driver.relation.one.time.consuming";

    /**
     * 被限流请求访问量
     */
    public static ResetCounter limitRequestCounter = metricRegistry.resetCounter(new MetricName(currentLimitingRequest, Maps.newHashMap()));

    /**
     * cache耗时
     */
    public static ResetTimer cacheTimeConsumingTimer = metricRegistry.resetTimer(new MetricName(cacheTimeConsuming, Maps.newHashMap()));

    /**
     * 命中缓存
     */
    public static ResetCounter cacheTimeHitCounter = metricRegistry.resetCounter(new MetricName(cacheTimeHit, Maps.newHashMap()));

    /**
     * 命中失效
     */
    public static ResetCounter cacheTimeMissCounter = metricRegistry.resetCounter(new MetricName(cacheTimeMiss, Maps.newHashMap()));

    /**
     * 命中失效二级缓存-司机姓名
     */
    public static Counter cacheTimeMissSKDrvNameCounter = metricRegistry.counter(new MetricName(cacheTimeSKDrvNameMiss, Maps.newHashMap()));

    /**
     * 命中失效二级缓存-司机电话
     */
    public static Counter cacheTimeMissSKDrvPhoneCounter = metricRegistry.counter(new MetricName(cacheTimeSKDrvPhoneMiss, Maps.newHashMap()));

    /**
     * 命中失效二级缓存-运力组
     */
    public static Counter cacheTimeMissSKDrvTransportCounter = metricRegistry.counter(new MetricName(cacheTimeSkTransportMiss, Maps.newHashMap()));

    /**
     * 有结果
     */
    public static ResetCounter hasResultCounter = metricRegistry.resetCounter(new MetricName(hasResult, Maps.newHashMap()));

    /**
     * 无结果
     */
    public static ResetCounter noResultCounter = metricRegistry.resetCounter(new MetricName(noResult, Maps.newHashMap()));

    /**
     * 一致性异常
     */
    public static Counter consistencyCounter = metricRegistry.counter(new MetricName(consistencyError, Maps.newHashMap()));

    /**
     * 分布式锁失败
     */
    public static Counter distributedLockFailCounter = metricRegistry.counter(new MetricName(distributedLockFail, Maps.newHashMap()));

    /**
     * 分布式锁异常
     */
    public static Counter distributedLockErrorCounter = metricRegistry.counter(new MetricName(distributedLockError, Maps.newHashMap()));

    /**
     * 分布式锁故障
     */
    public static Counter distributedLockFaultCounter = metricRegistry.counter(new MetricName(distributedLockFault, Maps.newHashMap()));

    /**
     * 报告上传请求量
     */
    public static Counter epidemicControlAllCounter = metricRegistry.counter(new MetricName(epidemicControlAll, Maps.newHashMap()));

    /**
     * 报告上传异常量
     */
    public static Counter epidemicControlErrorCounter = metricRegistry.counter(new MetricName(epidemicControlError, Maps.newHashMap()));

    /**
     * 需要复核
     */
    public static Counter epidemicControlNeedReviewCounter = metricRegistry.counter(new MetricName(epidemicControlNeedReview, Maps.newHashMap()));

    /**
     * 报告上传检验通过
     */
    public static Counter checkSuccessCounter = metricRegistry.counter(new MetricName(epidemicControlCheckPass, Maps.newHashMap()));

    /**
     * 报告上传检验失败
     */
    public static Counter checkFailCounter = metricRegistry.counter(new MetricName(epidemicControlCheckFail, Maps.newHashMap()));

    /**
     * 司机缓存保护拒绝打点
     */
    public static ResetCounter cacheProtectRefusedCounter = metricRegistry.resetCounter(new MetricName(cacheProtectRefused, Maps.newHashMap()));

    /**
     * 司机缓存 请求侧重点 司机
     */
    public static ResetCounter drvCacheReqFocusDriverCounter = metricRegistry.resetCounter(new MetricName(drvCacheReqFocusDriver, Maps.newHashMap()));
    /**
     * 司机缓存 请求DB 无结果
     */
    public static ResetCounter drvCacheReqDBNoResultCounter = metricRegistry.resetCounter(new MetricName(drvCacheReqDBNoResult, Maps.newHashMap()));

    /**
     * 司机缓存异常
     */
    public static ResetCounter drvCacheErrorCounter = metricRegistry.resetCounter(new MetricName(drvCacheError, Maps.newHashMap()));

    /**
     * 缓存 -司机数据miss
     */
    public static ResetCounter drvCacheDriverMissCounter = metricRegistry.resetCounter(new MetricName(drvCacheDriverMiss, Maps.newHashMap()));

    /**
     * 司机缓存 -车辆数据miss
     */
    public static ResetCounter drvCacheVehicleMissCounter = metricRegistry.resetCounter(new MetricName(drvCacheVehicleMiss, Maps.newHashMap()));

    /**
     * 缓存 -司机数据hit
     */
    public static ResetCounter drvCacheDriverHitCounter = metricRegistry.resetCounter(new MetricName(drvCacheDriverHit, Maps.newHashMap()));

    /**
     * 缓存 -车辆数据hit
     */
    public static ResetCounter drvCacheVehicleHitCounter = metricRegistry.resetCounter(new MetricName(drvCacheVehicleHit, Maps.newHashMap()));

    /**
     * 缓存 - 清除数据失败次数
     */
    public static ResetCounter drvCacheChallengeFailureCounter = metricRegistry.resetCounter(new MetricName(drvCacheChallengeFailure, Maps.newHashMap()));

    /**
     * TRocks 异常
     */
    public static ResetCounter transportTRocksErrorCounter = metricRegistry.resetCounter(new MetricName(transportTRocksError, Maps.newHashMap()));


    /**
     * 司机缓存大数据量请求记录
     * */
    private static final String drvCacheBigDataRecord  = "tms.transport.driver.cache.big.data";


    /**
     * 记录操作
     * */
    public static void operationRecordInc(String keyName, Map<String,String> tagMap) {
        ResetCounter counter = metricRegistry.resetCounter(new MetricName(new StringBuilder(transportResourceReqRecord).append(keyName).toString(), tagMap));
        counter.inc();
    }

    /**
     * 查询queryPointAreaInfo异常记录
     * */
    public static void queryPointAreaInfoErrorInc() {
        ResetCounter counter = metricRegistry.resetCounter(new MetricName(queryPointAreaInfoErrorReqRecord, Maps.newHashMap()));
        counter.inc();
    }

    /**
     * 查询 queryTransportGroupTakeOrderTimeScope异常记录
     * */
    public static void queryTransportGroupTakeOrderTimeScopeErrorInc() {
        ResetCounter counter = metricRegistry.resetCounter(new MetricName(queryTransportGroupTakeOrderTimeScopeErrorRecord, Maps.newHashMap()));
        counter.inc();
    }


    /**
     * 查询 queryDrvDriverListByAccountResDiffRecord SQL 不一致
     * */
    public static void queryDrvDriverListByAccountResDiffRecordInc() {
        ResetCounter counter = metricRegistry.resetCounter(new MetricName(queryDrvDriverListByAccountResDiffRecord, Maps.newHashMap()));
        counter.inc();
    }

    /**
     * 通过运力组id查询司机信息记录
     */
    public static void queryCacheByTransportRecordInc() {
        ResetCounter counter = metricRegistry.resetCounter(new MetricName(drvCacheReqFocusTransport, getClientTagRecordMap()));
        counter.inc();
    }

    /**
     * 司机关系查询整体批次耗时监控
     */
    public static ResetTimer queryDrvTransportRelationAllResetTimer() {
        return metricRegistry.resetTimer(new MetricName(transportDrvRelationAllTimeConsuming, Maps.newHashMap()));
    }

    /**
     * 司机关系查询单独批次耗时监控
     */
    public static ResetTimer queryDrvTransportRelationOneResetTimer() {
        return metricRegistry.resetTimer(new MetricName(transportDrvRelationOneTimeConsuming, Maps.newHashMap()));
    }

    /**
     * 查询司机缓存信息大请求量记录
     */
    public static void queryCacheBigDataRecordInc() {
        ResetCounter counter = metricRegistry.resetCounter(new MetricName(drvCacheBigDataRecord, getClientTagRecordMap()));
        counter.inc();
    }

    private static Map<String, String> getClientTagRecordMap() {
        Map<String, String> clientTagRecordMap = Maps.newHashMap();
        String clientId = PlatformUtil.getAppName();
        if (!Strings.isNullOrEmpty(clientId)) {
            clientTagRecordMap.put("clientAppid", clientId);
        }
        return clientTagRecordMap;
    }


}