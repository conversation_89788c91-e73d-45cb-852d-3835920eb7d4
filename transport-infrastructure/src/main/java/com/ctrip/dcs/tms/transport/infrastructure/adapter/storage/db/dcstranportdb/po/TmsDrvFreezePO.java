package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.annotation.*;

import javax.persistence.*;
import java.sql.*;

/**
 * <AUTHOR>
 * @date 2020-06-15
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "tms_drv_freeze")
public class TmsDrvFreezePO implements DalPojo {

    /**
     * 司机ID
     */
    @Id
	@Column(name = "drv_id")
	@Type(value = Types.BIGINT)
	private Long drvId;

    /**
     * 供应商ID
     */
	@Column(name = "supplier_id")
	@Type(value = Types.BIGINT)
	private Long supplierId;

    /**
     * 冻结状态(1.冻结，2.解冻)
     */
	@Column(name = "freeze_status")
	@Type(value = Types.TINYINT)
	private Integer freezeStatus;

    /**
     * 首次冻结时间
     */
	@Column(name = "first_freeze_time")
	@Type(value = Types.TIMESTAMP)
	private Timestamp firstFreezeTime;

    /**
     * 冻结小时(含判罚时间)
     */
	@Column(name = "freeze_hour")
	@Type(value = Types.INTEGER)
	private Integer freezeHour;

    /**
     * 记录每次操作冻结的来源，1.供应商,2.BD,3.判罚 以逗号分隔
     */
	@Column(name = "total_freeze_from")
	@Type(value = Types.VARCHAR)
	private String totalFreezeFrom;

    /**
     * 冻结原因(1车辆事故/故障,2司机违反平台规定,3车辆维修/保养,4服务问题冻结,5未级时参加培训,6冻结提现,7其它)
     */
	@Column(name = "freeze_reason")
	@Type(value = Types.VARCHAR)
	private String freezeReason;

    /**
     * 到期解冻后置操作（1.解冻后自动上线,2.解冻后自动下线）
     */
	@Column(name = "unfreeze_action")
	@Type(value = Types.TINYINT)
	private Integer unfreezeAction;

    /**
     * 冻结期间所有未服务订单(1.不改派,2.自动改派)
     */
	@Column(name = "freeze_order_set")
	@Type(value = Types.TINYINT)
	private Integer freezeOrderSet;

    /**
     * 解冻原因
     */
	@Column(name = "unfreeze_reason")
	@Type(value = Types.VARCHAR)
	private String unfreezeReason;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 操作时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

    /**
     * 创建人
     */
	@Column(name = "create_user")
	@Type(value = Types.VARCHAR)
	private String createUser;

    /**
     * 操作人
     */
	@Column(name = "modify_user")
	@Type(value = Types.VARCHAR)
	private String modifyUser;

    /**
     * 冻结次数
     */
    @Column(name = "freeze_count")
    @Type(value = Types.INTEGER)
    private Integer freezeCount;

    /**
     * 发送短信次数
     */
    @Column(name = "send_message_count")
    @Type(value = Types.INTEGER)
    private Integer sendMessageCount;

    /**
     * 因报告冻结状态
     */
    @Column(name = "report_freeze_status")
    @Type(value = Types.TINYINT)
    private Integer reportFreezeStatus;

    /**
     * 判罚冻结小时
     */
    @Column(name = "penalize_freeze_hour")
    @Type(value = Types.INTEGER)
    private Integer penalizeFreezeHour;

    /**
     * 判罚累计冻结小时
     */
    @Column(name = "penalize_accumulated_hour")
    @Type(value = Types.INTEGER)
    private Integer penalizeAccumulatedHour;

    /**
     * 确认上线标识(1.确认,0.未确认)
     */
    @Column(name = "confirm_online_status")
    @Type(value = Types.BIT)
    private Boolean confirmOnlineStatus;

    /**
     * 上云UDl
     */
    @Column(name = "provider_data_location")
    @Type(value = Types.VARCHAR)
    private String providerDataLocation;

    public String getProviderDataLocation() {
      return providerDataLocation;
    }

    public void setProviderDataLocation(String providerDataLocation) {
      this.providerDataLocation = providerDataLocation;
    }

    public Boolean getConfirmOnlineStatus() {
        return confirmOnlineStatus;
    }

    public void setConfirmOnlineStatus(Boolean confirmOnlineStatus) {
        this.confirmOnlineStatus = confirmOnlineStatus;
    }

    public Integer getReportFreezeStatus() {
        return reportFreezeStatus;
    }

    public void setReportFreezeStatus(Integer reportFreezeStatus) {
        this.reportFreezeStatus = reportFreezeStatus;
    }

    public Integer getPenalizeAccumulatedHour() {
        return penalizeAccumulatedHour;
    }

    public void setPenalizeAccumulatedHour(Integer penalizeAccumulatedHour) {
        this.penalizeAccumulatedHour = penalizeAccumulatedHour;
    }

    public Integer getPenalizeFreezeHour() {
        return penalizeFreezeHour;
    }

    public void setPenalizeFreezeHour(Integer penalizeFreezeHour) {
        this.penalizeFreezeHour = penalizeFreezeHour;
    }

    public Long getDrvId() {
        return drvId;
    }

    public void setDrvId(Long drvId) {
        this.drvId = drvId;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getFreezeStatus() {
        return freezeStatus;
    }

    public void setFreezeStatus(Integer freezeStatus) {
        this.freezeStatus = freezeStatus;
    }

    public Timestamp getFirstFreezeTime() {
        return firstFreezeTime;
    }

    public void setFirstFreezeTime(Timestamp firstFreezeTime) {
        this.firstFreezeTime = firstFreezeTime;
    }

    public Integer getFreezeHour() {
        return freezeHour;
    }

    public void setFreezeHour(Integer freezeHour) {
        this.freezeHour = freezeHour;
    }

    public String getTotalFreezeFrom() {
        return totalFreezeFrom;
    }

    public void setTotalFreezeFrom(String totalFreezeFrom) {
        this.totalFreezeFrom = totalFreezeFrom;
    }

    public String getFreezeReason() {
        return freezeReason;
    }

    public void setFreezeReason(String freezeReason) {
        this.freezeReason = freezeReason;
    }

    public Integer getUnfreezeAction() {
        return unfreezeAction;
    }

    public void setUnfreezeAction(Integer unfreezeAction) {
        this.unfreezeAction = unfreezeAction;
    }

    public Integer getFreezeOrderSet() {
        return freezeOrderSet;
    }

    public void setFreezeOrderSet(Integer freezeOrderSet) {
        this.freezeOrderSet = freezeOrderSet;
    }

    public String getUnfreezeReason() {
        return unfreezeReason;
    }

    public void setUnfreezeReason(String unfreezeReason) {
        this.unfreezeReason = unfreezeReason;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public Integer getFreezeCount() {
        return freezeCount;
    }

    public void setFreezeCount(Integer freezeCount) {
        this.freezeCount = freezeCount;
    }

    public Integer getSendMessageCount() {
        return sendMessageCount;
    }

    public void setSendMessageCount(Integer sendMessageCount) {
        this.sendMessageCount = sendMessageCount;
    }
}
