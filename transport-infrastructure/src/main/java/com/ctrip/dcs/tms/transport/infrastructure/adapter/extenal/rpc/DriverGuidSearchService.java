package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc;

import com.ctrip.igt.framework.soa.client.ServiceClient;
import com.ctrip.tour.driver.platform.api.contract.DriverPlatformApiServiceClient;
import com.ctrip.tour.driver.platform.api.contract.GetGrayControlRequestType;
import com.ctrip.tour.driver.platform.api.contract.GetGrayControlResponseType;
import com.ctrip.tour.driver.platform.api.contract.QueryDriverIdBySupplierRequestType;
import com.ctrip.tour.driver.platform.api.contract.QueryDriverIdBySupplierResponseType;
import com.ctrip.tour.driver.platform.api.contract.SearchDriverRequestType;
import com.ctrip.tour.driver.platform.api.contract.SearchDriverResponseType;
import com.ctrip.tour.driver.platform.api.contract.SearchVehicleRequestType;
import com.ctrip.tour.driver.platform.api.contract.SearchVehicleResponseType;

@ServiceClient(value = DriverPlatformApiServiceClient.class,format = "json")
public interface DriverGuidSearchService {

//  /**
//   * 根据条件搜索司机
//   * @param request
//   * @return
//   */
//  SearchDriverResponseType searchDriver(SearchDriverRequestType request);

  /**
   * 根据条件搜索车辆
   * @param request
   * @return
   */
  SearchVehicleResponseType searchVehicle(SearchVehicleRequestType request);

  GetGrayControlResponseType getGrayControl(GetGrayControlRequestType request);

  QueryDriverIdBySupplierResponseType queryDriverIdBySupplier(QueryDriverIdBySupplierRequestType request);

}
