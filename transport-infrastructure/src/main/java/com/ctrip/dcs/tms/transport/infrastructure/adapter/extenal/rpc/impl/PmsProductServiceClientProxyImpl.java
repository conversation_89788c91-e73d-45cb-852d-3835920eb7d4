package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.impl;

import com.ctrip.dcs.pms.product.api.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.PmsProductService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * <AUTHOR>
 * @Date 2020/4/16 20:11
 */
@Service
public class PmsProductServiceClientProxyImpl implements PmsProductServiceClientProxy {

    @Autowired
    private PmsProductService pmsProductService;

    @Override
    public GetSystemDictionaryResponseType getSystemDictionary(GetSystemDictionaryRequestType request) throws Exception {
        CtripCommonUtils.initRequestHeader(request);
        return pmsProductService.getSystemDictionary(request);
    }

    @Override
    public QueryProductSkuListResponseType queryProductSkuList(QueryProductSkuListRequestType request) throws Exception {
        CtripCommonUtils.initRequestHeader(request);
        return pmsProductService.queryProductSkuList(request);
    }

    @Override
    public QueryProductSkuWithRegionListResponseType queryProductSkuWithRegionList(QueryProductSkuWithRegionListRequestType regionListRequestType) {
        try {
            CtripCommonUtils.initRequestHeader(regionListRequestType);
            return pmsProductService.queryProductSkuWithRegionList(regionListRequestType);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
