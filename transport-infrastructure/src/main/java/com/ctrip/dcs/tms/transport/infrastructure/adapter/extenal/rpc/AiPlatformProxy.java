package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.ctrip.basebiz.ai.aiplatform.contract.api.AiPlatformServiceClient;

@Configuration
public class AiPlatformProxy {

    @Bean("aiPlatformServiceClient")
    public AiPlatformServiceClient aiPlatformServiceClient() {
        AiPlatformServiceClient aiPlatformServiceClient = AiPlatformServiceClient.getInstance();
        aiPlatformServiceClient.setCurrentSocketTimeout(30);
        return aiPlatformServiceClient;
    }
}
