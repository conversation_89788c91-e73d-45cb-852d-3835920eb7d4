package com.ctrip.dcs.tms.transport.infrastructure.common.util;

import java.time.Instant;
import java.util.Optional;

import com.ctrip.framework.foundation.Foundation;

/**
 * <AUTHOR>
 * @date 2021/11/17 21:12
 * @description
 */
public class LocalSnowFlakeUtil {

    private final long twepoch = 1288834974657L;
    private final long workerIdBits = 5L;
    private final long datacenterIdBits = 5L;
    private final long maxWorkerId = -1L ^ (-1L << workerIdBits);
    private final long maxDatacenterId = -1L ^ (-1L << datacenterIdBits);
    private final long sequenceBits = 12L;
    private final long workerIdShift = sequenceBits;
    private final long datacenterIdShift = sequenceBits + workerIdBits;
    private final long timestampLeftShift = sequenceBits + workerIdBits + datacenterIdBits;
    private final long sequenceMask = -1L ^ (-1L << sequenceBits);

    private long workerId;
    private long datacenterId;
    private long sequence = 0L;
    private long lastTimestamp = -1L;


    public LocalSnowFlakeUtil() {
        long wId = Math.abs(
                Optional.ofNullable(Foundation.net().getHostAddress()).orElse(String.valueOf(Instant.now().toEpochMilli()))
                        .hashCode() % maxWorkerId);
        long dId = Math.abs(
                Optional.ofNullable(Foundation.server().getDataCenter()).orElse(String.valueOf(Instant.now().toEpochMilli()))
                        .hashCode() % maxDatacenterId);
        this.workerId = Math.abs(wId);
        this.datacenterId = Math.abs(dId);
    }

    /**
     * 下一个ID
     *
     * @return ID
     */
    public synchronized long nextId() {
        long timestamp = Instant.now().toEpochMilli();
        if (timestamp < lastTimestamp) {
            throw new IllegalStateException(String
                    .format("Clock moved backwards. Refusing to generate id for %d milliseconds", lastTimestamp - timestamp));
        }
        if (lastTimestamp == timestamp) {
            sequence = (sequence + 1) & sequenceMask;
            if (sequence == 0) {
                timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            sequence = 0L;
        }
        lastTimestamp = timestamp;
        return ((timestamp - twepoch) << timestampLeftShift) | (datacenterId << datacenterIdShift) | (workerId
                << workerIdShift) | sequence;
    }

    private long tilNextMillis(long lastTimestamp) {
        long timestamp = Instant.now().toEpochMilli();
        while (timestamp <= lastTimestamp) {
            timestamp = Instant.now().toEpochMilli();
        }
        return timestamp;
    }
}