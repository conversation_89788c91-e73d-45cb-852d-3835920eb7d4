package com.ctrip.dcs.tms.transport.infrastructure.common.util;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.language.*;
import com.ctrip.igt.framework.common.result.Result;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.*;
import com.google.common.collect.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.*;
import org.springframework.beans.*;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventType.OCR_RES;

public class CtripCommonUtils {

    private static final String FAIL = "1";

    public static void initRequestHeader(final HasIGTRequestHeader igtRequestHeader){
        Language language = LanguageContext.getLanguage();
        if (Objects.isNull(language)){
            return;
        }
        RequestHeader requestHeader = igtRequestHeader.getRequestHeader();
        if (Objects.isNull(requestHeader)){
            requestHeader = new RequestHeader();
            igtRequestHeader.setRequestHeader(requestHeader);
        }
        requestHeader.setLanguageCode(language.getLanguageCode());
        requestHeader.setLanguage(language.getLanguage());
        requestHeader.setLocale(language.getLocaleCode());
    }

    public static CertificateResultSOADTO resultSOADTO(TmsCertificateCheckPO checkPO){
        if(Objects.isNull(checkPO)){
            return null;
        }
        CertificateResultSOADTO soadto = new CertificateResultSOADTO();
        BeanUtils.copyProperties(checkPO,soadto);
        soadto.setCheckTime(DateUtil.timestampToString(checkPO.getDatachangeLasttime(),DateUtil.YYYYMMDDHHMMSS));
        if(StringUtils.isNotEmpty(checkPO.getCheckResult()) && !Objects.equals("[]",checkPO.getCheckResult())){
            soadto.setInfoList(JsonUtil.fromJson(checkPO.getCheckResult(), new TypeReference<List<CertificateCheckSOAInfo>>() {
            }));
        }else{
            if(Objects.equals(checkPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode())){
                List<CertificateCheckSOAInfo> infoList = Lists.newArrayList();
                CertificateCheckSOAInfo info = new CertificateCheckSOAInfo();
                info.setColumnValue("网约车驾驶员证信息");
                infoList.add(info);
                soadto.setInfoList(infoList);
            }
            if(Objects.equals(checkPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode())){
                List<CertificateCheckSOAInfo> infoList = Lists.newArrayList();
                CertificateCheckSOAInfo info = new CertificateCheckSOAInfo();
                info.setColumnValue("网约车运输证信息");
                infoList.add(info);
                soadto.setInfoList(infoList);
            }
        }
        if(Objects.equals(checkPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.IDCARD.getCode())){
            List<CertificateCheckSOAInfo> infoList = Lists.newArrayList();
            CertificateCheckSOAInfo info = new CertificateCheckSOAInfo();
            info.setColumnValue("背景审核");
            info.setResultValue("超过48小时无任何返回信息");
            infoList.add(info);
            soadto.setInfoList(infoList);
        }
        return soadto;
    }

    public static Boolean queryActiveChoose(Boolean active) {
        return active == null ? Boolean.TRUE : active;
    }

    /**
    　* @description: 判断司机冻结-是否都是供应商冻结
    　* <AUTHOR>
    　* @date 2022/10/17 10:53
    */
    public static Boolean judgeFreezeFromIsSupplier(String freezeFrom) {
        if (StringUtils.isEmpty(freezeFrom)) {
            return Boolean.FALSE;
        }
        List<Integer> fromLists = Arrays.stream(freezeFrom.split(TmsTransportConstant.SPLIT))
                .map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fromLists)) {
            return Boolean.FALSE;
        }
        for (Integer from : fromLists) {
            if (!Objects.equals(from, CommonEnum.FreezeOPFromEnum.SUPPLIER.getValue())) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    public static CertificateResultSOADTO resultPassData(){
        CertificateResultSOADTO soadto = new CertificateResultSOADTO();
        soadto.setCheckStatus(TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
        return soadto;
    }

    public static List<CertificateResultSOADTO> assembleCheckData(Map<Integer, TmsCertificateCheckPO> checkPOMap){
        if(MapUtils.isEmpty(checkPOMap)){
            return Collections.emptyList();
        }
        List<CertificateResultSOADTO> resultSOADTOS = Lists.newArrayList();
        for(Map.Entry<Integer, TmsCertificateCheckPO> entry : checkPOMap.entrySet()){
            CertificateResultSOADTO soadto = resultSOADTO(entry.getValue());
            if(!Objects.isNull(soadto)){
                resultSOADTOS.add(soadto);
            }
        }
        return resultSOADTOS;
    }

    public static Result<DrvDriverPO> resultDrvAccountErrorInfo(String code){
        switch (code){
            case "400": return rsultCode(SharkUtils.getSharkValueDefault(SharkKeyConstant.driverLoginPasswordEmpty),code);
            case "401": return rsultCode(SharkUtils.getSharkValueDefault(SharkKeyConstant.driverLoginAuthcodeFail),code);
            case "402": return rsultCode(SharkUtils.getSharkValueDefault(SharkKeyConstant.driverLoginInformationDuplicate),code);
            case "403": return rsultCode(SharkUtils.getSharkValueDefault(SharkKeyConstant.driverLoginAuthfail),code);
            case "404": return rsultCode(SharkUtils.getSharkValueDefault(SharkKeyConstant.driverLoginNotexist),code);
            case "405": return rsultCode(SharkUtils.getSharkValueDefault(SharkKeyConstant.driverLoginPassworderror),code);
            case "406": return rsultCode(SharkUtils.getSharkValueDefault(SharkKeyConstant.driverLoginPassswordNotconsistent),code);
            case "407": return rsultCode(SharkUtils.getSharkValueDefault(SharkKeyConstant.driverLoginResetPasswordFail),code);
        }
        return rsultCode("result error","500");
    }

    public static Result<DrvDriverPO> rsultCode(String msg, String code){
        return Result.Builder.<DrvDriverPO>newResult().fail().withMsg(msg).withCode(code).build();
    }

    public static Boolean ocrIsPass(List<OcrPassStatusModelSOA> passList){
        if(org.apache.commons.collections.CollectionUtils.isEmpty(passList)){
            return Boolean.FALSE;
        }
        //打点
        passList.forEach(p -> {
            String eventKey = String.valueOf(p.getOcrItem());
            if (TmsTransportConstant.OverseasOCRPassStatusEnum.pass.getCode().equals(p.getPassStatus())) {
                Cat.logEvent(OCR_RES, eventKey);
            } else {
                Cat.logEvent(OCR_RES, eventKey, FAIL, StringUtils.EMPTY);
            }
        });
        for (OcrPassStatusModelSOA modelSOA : passList){
            if(modelSOA.getPassStatus() == null || Objects.equals(TmsTransportConstant.OverseasOCRPassStatusEnum.no_pass.getCode(),modelSOA.getPassStatus())){
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    //判断id是否在配置中，-1代表全量配置中
    public static Boolean verdictIdConfInside(Long id,String conf){
        if(StringUtils.isEmpty(conf) || id == null || id <= 0){
            return Boolean.FALSE;
        }
        Set<Long> confIdList = BaseUtil.getLongSet(conf);
        //-1 代表开全量
        if(confIdList.contains(id) || confIdList.contains(-1L)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    //根据临时派遣标识返回已存在的数据相应code
    public static Result<Boolean> resultDrvUniquenessCode(Integer temporaryDispatchMark){
        //查询当前重复数据是正式数据还是临派数据
        switch (temporaryDispatchMark){
            case 0:return Result.Builder.<Boolean>newResult().fail().withCode(TmsTransportConstant.ResultCode.code100028).build();
            case 1:return Result.Builder.<Boolean>newResult().fail().withCode(TmsTransportConstant.ResultCode.code100029).build();
        }
        return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
    }
    //根据临时派遣标识返回已存在的数据相应code
    public static Result<Boolean> resultVehUniquenessCode(Integer temporaryDispatchMark){
        //查询当前重复数据是正式数据还是临派数据
        switch (temporaryDispatchMark){
            case 0:return Result.Builder.<Boolean>newResult().fail().withCode(TmsTransportConstant.ResultCode.code100030).build();
            case 1:return Result.Builder.<Boolean>newResult().fail().withCode(TmsTransportConstant.ResultCode.code100031).build();
        }
        return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
    }
}
