package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model;

import com.ctrip.dcs.tms.transport.api.model.TransportGroup;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class DriverGuideDTO extends DrvDriverPO {

  /**
   * *车辆品牌Id
   */
  public Long carBrandId;

  /**
   * *车辆品牌名称
   */
  public String carBrandName;

  /**
   * 车型Id
   */
  public Integer carTypeId;

  /**
   * *车型名称
   */
  public String carTypeName;

  /**
   * *车辆颜色Id
   */
  public Long carColorId;

  /**
   * *车辆颜色
   */
  public String carColor;

  /**
   * *车系id
   */
  public Long carSeriesId;

  /**
   * *车系名称
   */
  public String carSeriesName;


  /**
   * *是否听播报 0-不听，1-听
   */
  public Integer broadcast;

  /**
   * *是否传工作时段 	0-不传 1-传
   */
  public Integer isSendWorkPeriod;

  /**
   * *兼容合作模式 0-全职 1-兼职
   */
  public Integer compatibleCoopMode;

  private String cityName;

  private String vehTemporaryEndTime;
  private String drvTemporaryEndTime;
  private Integer temporaryVehicleMark;
  private List<Long> dispatchSupplierIdList;
  private String vehicleNetCertNo;
  private String realPicUrl;
  private String vehicleFullImg;
  private String vehCreateTime;
  private String vehRegstDate;
  private String drvConnectAddress;
  private Boolean rideHailingDrvCertValid;
  private Boolean rideHailingVehCertValid;
  private String vin;
  private Integer vehicleStatus;
  private List<Integer> vehProductionLineCodeList;
  private List<Integer> drvProductionLineCodeList;
  private String supplierName;
  private Integer maxPassengers;
  private Integer maxLuggages;
  private String picUrl;
  private String qunarCityCode;
  private List<TransportGroup> transportGroups;
  private String createTime;
  private List<String> workTimes;
  private Integer isEnergy;
  private Double addressLatitude;
  private Double addressLongitude;
  private String drvStatusName;
  private String drvFromName;
  private Integer areaScope;
  private String vehicleTypeName;
  private Timestamp firstFreezeTime;

  public String getDriverName() {
    return getDrvName();
  }



  /**
   * 司机电话 明文
   */
  public String getDriverPhone() {
    return getDrvPhone();
  }

  /**
   * *司机手机区号
   */
  public String getPhoneAreaCode() {
    return getIgtCode();
  }

  public Long getCarId() {
    return getVehicleId();
  }

  public String getCarLicense() {
    return getVehicleLicense();
  }


  public Long gteDriverId() {
    return getDrvId();
  }
}
