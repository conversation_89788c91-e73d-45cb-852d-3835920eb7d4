package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc;

import com.ctrip.dcs.location.service.interfaces.*;
import com.ctrip.dcs.location.service.interfaces.message.*;
import com.ctrip.igt.framework.soa.client.*;

/**
 * <AUTHOR>
 */
@ServiceClient(value = LocationQueryServiceClient.class,format = "json")
public interface LocationQueryService {

    QueryDriverLocationResponseType queryDriverLocation(QueryDriverLocationRequestType request);

}