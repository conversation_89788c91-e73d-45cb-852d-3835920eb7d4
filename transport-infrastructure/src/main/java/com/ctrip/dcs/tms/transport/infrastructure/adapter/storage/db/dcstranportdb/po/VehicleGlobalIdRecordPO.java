package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.annotation.*;

import javax.persistence.*;
import java.sql.*;


@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "tms_vehicle_global_id_record")
public class VehicleGlobalIdRecordPO {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  @Type(value = Types.BIGINT)
  private Long id;

  @Column(name = "vehicle_license")
  @Type(value = Types.VARCHAR)
  private String vehicleLicense;

  @Column(name = "source")
  @Type(value = Types.VARCHAR)
  private String source;

  /**
   * 创建时间
   */
  @Column(name = "datachange_createtime")
  @Type(value = Types.TIMESTAMP)
  private Timestamp datachangeCreatetime;


  /**
   * 更新时间
   */
  @Column(name = "datachange_lasttime")
  @Type(value = Types.TIMESTAMP)
  private Timestamp datachangeLasttime;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getVehicleLicense() {
    return vehicleLicense;
  }

  public void setVehicleLicense(String vehicleLicense) {
    this.vehicleLicense = vehicleLicense;
  }

  public String getSource() {
    return source;
  }

  public void setSource(String source) {
    this.source = source;
  }

  public Timestamp getDatachangeCreatetime() {
    return datachangeCreatetime;
  }

  public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
    this.datachangeCreatetime = datachangeCreatetime;
  }

  public Timestamp getDatachangeLasttime() {
    return datachangeLasttime;
  }

  public void setDatachangeLasttime(Timestamp datachangeLasttime) {
    this.datachangeLasttime = datachangeLasttime;
  }
}
