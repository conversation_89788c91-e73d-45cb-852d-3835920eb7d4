package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OverageDTO;

import qunar.tc.qconfig.client.JsonConfig;

@Component
public class OverageQConfig {
    JsonConfig<List<OverageDTO>> config = JsonConfig.get("vehicle.overage.json", JsonConfig.ParameterizedClass.of(List.class, OverageDTO.class));

    public static final Double overage = 5D;

    public OverageDTO getOverageMap(Long cityId, Long vehicleTypeId) {
        List<OverageDTO> current = config.current();
        Map<Long, List<OverageDTO>> collect = current.stream().collect(Collectors.groupingBy(OverageDTO::getCityId));
        List<OverageDTO> overageDTOS = collect.get(cityId);
        if (CollectionUtils.isEmpty(overageDTOS)) {
            return defaultData(cityId, vehicleTypeId);
        }
        Map<Long, OverageDTO> collect1 = overageDTOS.stream().collect(Collectors.toMap(OverageDTO::getVehicleTypeId, a -> a, (k1, k2) -> k1));
        OverageDTO overageDTO = collect1.get(vehicleTypeId);
        return Objects.nonNull(overageDTO) ? overageDTO : defaultData(cityId, vehicleTypeId);
    }

    public OverageDTO defaultData(Long cityId, Long vehicleTypeId){
        OverageDTO overageDTO = new OverageDTO();
        overageDTO.setCityId(cityId);
        overageDTO.setVehicleTypeId(vehicleTypeId);
        // fixme 魔法值
        overageDTO.setAccessLimit(overage - 0.25D);
        overageDTO.setOverage(overage);
        return overageDTO;
    }
}
