package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import qunar.tc.qconfig.client.spring.QMapConfig;
import com.fasterxml.jackson.core.type.*;
import org.springframework.stereotype.*;

import java.util.*;

@Component
@QMapConfig("certificate.config.white.properties")
public class TmsCertificateConfigWhiteQconfig {

    //司机证件配置白名单
    String drvCertificateConfigWhite;

    //车辆证件配置白名单
    String vehCertificateConfigWhite;

    public List<Long> getDrvCertificateConfigWhiteList() {
        return JsonUtil.fromJson(drvCertificateConfigWhite, new TypeReference<List<Long>>() { });
    }

    public List<Long> getVehCertificateConfigWhiteList() {
        return JsonUtil.fromJson(vehCertificateConfigWhite, new TypeReference<List<Long>>() { });
    }

    public String getDrvCertificateConfigWhite() {
        return drvCertificateConfigWhite;
    }

    public void setDrvCertificateConfigWhite(String drvCertificateConfigWhite) {
        this.drvCertificateConfigWhite = drvCertificateConfigWhite;
    }

    public String getVehCertificateConfigWhite() {
        return vehCertificateConfigWhite;
    }

    public void setVehCertificateConfigWhite(String vehCertificateConfigWhite) {
        this.vehCertificateConfigWhite = vehCertificateConfigWhite;
    }
}
