package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.Feature;
import qunar.tc.qconfig.client.MapConfig;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.Map;


@Component
@QMapConfig("nephele.properties")
@Getter
@Setter
public class NephelpConfig {
    private String channel;

    private String fileUploadEndpoint;
}
