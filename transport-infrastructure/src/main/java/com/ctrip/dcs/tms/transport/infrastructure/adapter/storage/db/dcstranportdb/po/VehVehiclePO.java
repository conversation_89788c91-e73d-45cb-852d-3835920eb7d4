package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.annotation.*;

import javax.persistence.*;
import java.sql.*;

/**
 * <AUTHOR>
 * @date 2020-02-20
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "veh_vehicle")
public class VehVehiclePO implements DalPojo {

    /**
     * 车辆主键
     */
    @Id
	@Column(name = "vehicle_id")
	@Type(value = Types.BIGINT)
	private Long vehicleId;

    /**
     * 车牌号
     */
	@Column(name = "vehicle_license")
	@Type(value = Types.VARCHAR)
	private String vehicleLicense;

    /**
     * 供应商ID
     */
	@Column(name = "supplier_id")
	@Type(value = Types.BIGINT)
	private Long supplierId;

    /**
     * 国家码
     */
	@Column(name = "country_id")
	@Type(value = Types.BIGINT)
	private Long countryId;

    /**
     * 国家名称
     */
	@Column(name = "country_name")
	@Type(value = Types.VARCHAR)
	private String countryName;

    /**
     * 城市
     */
	@Column(name = "city_id")
	@Type(value = Types.BIGINT)
	private Long cityId;

    /**
     * 车型ID
     */
	@Column(name = "vehicle_type_id")
	@Type(value = Types.BIGINT)
	private Long vehicleTypeId;

    /**
     * 车辆品牌ID
     */
	@Column(name = "vehicle_brand_id")
	@Type(value = Types.BIGINT)
	private Long vehicleBrandId;

    /**
     * 车系
     */
	@Column(name = "vehicle_series")
	@Type(value = Types.BIGINT)
	private Long vehicleSeries;

    /**
     * 车身颜色ID
     */
	@Column(name = "vehicle_color_id")
	@Type(value = Types.BIGINT)
	private Long vehicleColorId;

    /**
     * 车辆能源类型：1燃油车辆，2纯电动车辆，3油电混合车辆
     */
	@Column(name = "vehicle_energy_type")
	@Type(value = Types.TINYINT)
	private Integer vehicleEnergyType;

    /**
     * vin码
     */
	@Column(name = "vin")
	@Type(value = Types.VARCHAR)
	private String vin;

    /**
     * 车辆首次注册日期
     */
	@Column(name = "regst_date")
	@Type(value = Types.DATE)
	private Date regstDate;

    /**
     * 使用性质(1营运、2非营运、3租赁、4出租客运、5预约出租客运、6营转非)
     */
	@Column(name = "using_nature")
	@Type(value = Types.TINYINT)
	private Integer usingNature;

    /**
     * 网约车运输证图片
     */
	@Column(name = "net_tans_ctfct_img")
	@Type(value = Types.VARCHAR)
	private String netTansCtfctImg;

    /**
     * 车辆行驶证照片
     */
	@Column(name = "vehicle_certi_img")
	@Type(value = Types.VARCHAR)
	private String vehicleCertiImg;

    /**
     * 车身照片
     */
	@Column(name = "vehicle_full_img")
	@Type(value = Types.VARCHAR)
	private String vehicleFullImg;

    /**
     * 车前排照片
     */
	@Column(name = "vehicle_front_img")
	@Type(value = Types.VARCHAR)
	private String vehicleFrontImg;

    /**
     * 车后排照片
     */
	@Column(name = "vehicle_back_img")
	@Type(value = Types.VARCHAR)
	private String vehicleBackImg;

    /**
     * 后备箱照片
     */
	@Column(name = "vehicle_trunk_img")
	@Type(value = Types.VARCHAR)
	private String vehicleTrunkImg;

    /**
     * 有否绑定司机(0无,1有)
     */
	@Column(name = "has_drv")
	@Type(value = Types.BIT)
	private Boolean hasDrv;

    /**
     * 备注
     */
	@Column(name = "comments")
	@Type(value = Types.VARCHAR)
	private String comments;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 创建人
     */
	@Column(name = "create_user")
	@Type(value = Types.VARCHAR)
	private String createUser;

    /**
     * 操作人
     */
	@Column(name = "modify_user")
	@Type(value = Types.VARCHAR)
	private String modifyUser;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	/**
	 * 车辆状态  0.未激活,1.上线.3.下线
	 */
	@Column(name = "vehicle_status")
	@Type(value = Types.TINYINT)
	private Integer vehicleStatus;

	/**
	 * 行驶证所有人
	 */
	@Column(name = "vehicle_license_owner")
	@Type(value = Types.VARCHAR)
	private String vehicleLicenseOwner;

	/**
	 * 逻辑产线
	 */
	@Column(name = "category_synthesize_code")
	@Type(value = Types.TINYINT)
	private Integer categorySynthesizeCode;

	/**
	 * 审批状态(0.暂不可审批、1.待审批、2.审批通过、3.审批驳回)
	 */
	@Column(name = "approve_status")
	@Type(value = Types.TINYINT)
	private Integer approveStatus;

	/**
	 * 网约车申诉材料
	 */
	@Column(name = "net_appeal_materials")
	@Type(value = Types.VARCHAR)
	private String netAppealMaterials;

	/**
	 * 车牌号所属城市ID
	 */
	@Column(name = "vehicle_license_city_id")
	@Type(value = Types.BIGINT)
	private Long vehicleLicenseCityId;

	/**
	 * 版本标识(1.第一版,2.第二版)
	 */
	@Column(name = "version_flag")
	@Type(value = Types.INTEGER)
	private Integer versionFlag;


	/**
	 * 保存最初的证件配置值
	 */
	@Column(name = "certificate_config")
	@Type(value = Types.VARCHAR)
	private String certificateConfig;

	/**
	 * OCR识别值
	 */
	@Column(name = "ocr_field_value")
	@Type(value = Types.VARCHAR)
	private String ocrFieldValue;

	/**
	 * 车辆行驶证申诉材料
	 */
	@Column(name = "vehicle_license_appeal_materials")
	@Type(value = Types.VARCHAR)
	private String vehicleLicenseAppealMaterials;

	/**
	 * 网约车运输证号
	 */
	@Column(name = "vehicle_net_cert_no")
	@Type(value = Types.VARCHAR)
	private String vehicleNetCertNo;

	/**
	 * 1表示启用，0表示删除
	 */
	@Column(name = "active")
	@Type(value = Types.BIT)
	private Boolean active;

	/**
	 * OCR是否通过,0.不通过,1-通过，默认不通过
	 */
	@Column(name = "ocr_pass_status")
	@Type(value = Types.INTEGER)
	private Boolean ocrPassStatus;

	/**
	 * OCR各项校验结果
	 */
	@Column(name = "ocr_pass_status_json")
	@Type(value = Types.VARCHAR)
	private String ocrPassStatusJson;

	/**
	 * 审批状态
	 * 1.未处理
	 * 2.合规
	 * 3.不合规
	 */
	@Column(name = "audit_status")
	@Type(value = Types.TINYINT)
	private Integer auditStatus;

	/**
	 * 临时派遣标签(0-正式,1-临派)
	 */
	@Column(name = "temporary_dispatch_mark")
	@Type(value = Types.INTEGER)
	private Integer temporaryDispatchMark;

	/**
	 * 车龄类型
	 */
	@Column(name = "vehicle_age_type")
	@Type(value = Types.VARCHAR)
	private String vehicleAgeType;

	/**
	 * 临时派遣结束时间
	 */
	@Column(name = "temporary_dispatch_end_datetime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp temporaryDispatchEndDatetime;

	/**
	 * 车辆保险单
	 */
	@Column(name = "vehicle_insurance_policy")
	@Type(value = Types.VARCHAR)
	private String vehicleInsurancePolicy;

	/**
	 * 合规资质证件
	 */
	@Column(name = "compliance_qualification_certificates")
	@Type(value = Types.VARCHAR)
	private String complianceQualificationCertificates;

	/**
	 * 新OCR识别值
	 */
	@Column(name = "new_ocr_field_value")
	@Type(value = Types.VARCHAR)
	private String newOcrFieldValue;

	public Integer getTemporaryDispatchMark() {
		return temporaryDispatchMark;
	}

	public void setTemporaryDispatchMark(Integer temporaryDispatchMark) {
		this.temporaryDispatchMark = temporaryDispatchMark;
	}

	public Timestamp getTemporaryDispatchEndDatetime() {
		return temporaryDispatchEndDatetime;
	}

	public void setTemporaryDispatchEndDatetime(Timestamp temporaryDispatchEndDatetime) {
		this.temporaryDispatchEndDatetime = temporaryDispatchEndDatetime;
	}

	public Boolean getOcrPassStatus() {
		return ocrPassStatus;
	}

	public void setOcrPassStatus(Boolean ocrPassStatus) {
		this.ocrPassStatus = ocrPassStatus;
	}

	public String getOcrPassStatusJson() {
		return ocrPassStatusJson;
	}

	public void setOcrPassStatusJson(String ocrPassStatusJson) {
		this.ocrPassStatusJson = ocrPassStatusJson;
	}

	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}

	public String getVehicleLicenseAppealMaterials() {
		return vehicleLicenseAppealMaterials;
	}

	public void setVehicleLicenseAppealMaterials(String vehicleLicenseAppealMaterials) {
		this.vehicleLicenseAppealMaterials = vehicleLicenseAppealMaterials;
	}

	public String getCertificateConfig() {
		return certificateConfig;
	}

	public void setCertificateConfig(String certificateConfig) {
		this.certificateConfig = certificateConfig;
	}

	public String getOcrFieldValue() {
		return ocrFieldValue;
	}

	public void setOcrFieldValue(String ocrFieldValue) {
		this.ocrFieldValue = ocrFieldValue;
	}

	public Integer getVersionFlag() {
		return versionFlag;
	}

	public void setVersionFlag(Integer versionFlag) {
		this.versionFlag = versionFlag;
	}

	public String getNetAppealMaterials() {
		return netAppealMaterials;
	}

	public void setNetAppealMaterials(String netAppealMaterials) {
		this.netAppealMaterials = netAppealMaterials;
	}

	public Integer getCategorySynthesizeCode() {
		return categorySynthesizeCode;
	}

	public void setCategorySynthesizeCode(Integer categorySynthesizeCode) {
		this.categorySynthesizeCode = categorySynthesizeCode;
	}

	public Long getVehicleId() {
		return vehicleId;
	}

	public void setVehicleId(Long vehicleId) {
		this.vehicleId = vehicleId;
	}

	public String getVehicleLicense() {
		return vehicleLicense;
	}

	public void setVehicleLicense(String vehicleLicense) {
		this.vehicleLicense = vehicleLicense;
	}

	public Long getSupplierId() {
		return supplierId;
	}

	public void setSupplierId(Long supplierId) {
		this.supplierId = supplierId;
	}

	public Long getCountryId() {
		return countryId;
	}

	public void setCountryId(Long countryId) {
		this.countryId = countryId;
	}

	public String getCountryName() {
		return countryName;
	}

	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	public Long getCityId() {
		return cityId;
	}

	public void setCityId(Long cityId) {
		this.cityId = cityId;
	}

	public Long getVehicleTypeId() {
		return vehicleTypeId;
	}

	public void setVehicleTypeId(Long vehicleTypeId) {
		this.vehicleTypeId = vehicleTypeId;
	}

	public Long getVehicleBrandId() {
		return vehicleBrandId;
	}

	public void setVehicleBrandId(Long vehicleBrandId) {
		this.vehicleBrandId = vehicleBrandId;
	}

	public Long getVehicleSeries() {
		return vehicleSeries;
	}

	public void setVehicleSeries(Long vehicleSeries) {
		this.vehicleSeries = vehicleSeries;
	}

	public Long getVehicleColorId() {
		return vehicleColorId;
	}

	public void setVehicleColorId(Long vehicleColorId) {
		this.vehicleColorId = vehicleColorId;
	}

	public Integer getVehicleEnergyType() {
		return vehicleEnergyType;
	}

	public void setVehicleEnergyType(Integer vehicleEnergyType) {
		this.vehicleEnergyType = vehicleEnergyType;
	}

	public String getVin() {
		return vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public Date getRegstDate() {
		return regstDate;
	}

	public void setRegstDate(Date regstDate) {
		this.regstDate = regstDate;
	}

	public Integer getUsingNature() {
		return usingNature;
	}

	public void setUsingNature(Integer usingNature) {
		this.usingNature = usingNature;
	}

	public String getNetTansCtfctImg() {
		return netTansCtfctImg;
	}

	public void setNetTansCtfctImg(String netTansCtfctImg) {
		this.netTansCtfctImg = netTansCtfctImg;
	}

	public String getVehicleCertiImg() {
		return vehicleCertiImg;
	}

	public void setVehicleCertiImg(String vehicleCertiImg) {
		this.vehicleCertiImg = vehicleCertiImg;
	}

	public String getVehicleFullImg() {
		return vehicleFullImg;
	}

	public void setVehicleFullImg(String vehicleFullImg) {
		this.vehicleFullImg = vehicleFullImg;
	}

	public String getVehicleFrontImg() {
		return vehicleFrontImg;
	}

	public void setVehicleFrontImg(String vehicleFrontImg) {
		this.vehicleFrontImg = vehicleFrontImg;
	}

	public String getVehicleBackImg() {
		return vehicleBackImg;
	}

	public void setVehicleBackImg(String vehicleBackImg) {
		this.vehicleBackImg = vehicleBackImg;
	}

	public String getVehicleTrunkImg() {
		return vehicleTrunkImg;
	}

	public void setVehicleTrunkImg(String vehicleTrunkImg) {
		this.vehicleTrunkImg = vehicleTrunkImg;
	}

	public Boolean getHasDrv() {
		return hasDrv;
	}

	public void setHasDrv(Boolean hasDrv) {
		this.hasDrv = hasDrv;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public Timestamp getDatachangeCreatetime() {
		return datachangeCreatetime;
	}

	public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
		this.datachangeCreatetime = datachangeCreatetime;
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public String getModifyUser() {
		return modifyUser;
	}

	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public Integer getVehicleStatus() {
		return vehicleStatus;
	}

	public void setVehicleStatus(Integer vehicleStatus) {
		this.vehicleStatus = vehicleStatus;
	}

	public String getVehicleLicenseOwner() {
		return vehicleLicenseOwner;
	}

	public void setVehicleLicenseOwner(String vehicleLicenseOwner) {
		this.vehicleLicenseOwner = vehicleLicenseOwner;
	}

	public Integer getApproveStatus() {
		return approveStatus;
	}

	public void setApproveStatus(Integer approveStatus) {
		this.approveStatus = approveStatus;
	}

	public Long getVehicleLicenseCityId() {
		return vehicleLicenseCityId;
	}

	public void setVehicleLicenseCityId(Long vehicleLicenseCityId) {
		this.vehicleLicenseCityId = vehicleLicenseCityId;
	}

	public String getVehicleNetCertNo() {
		return vehicleNetCertNo;
	}

	public void setVehicleNetCertNo(String vehicleNetCertNo) {
		this.vehicleNetCertNo = vehicleNetCertNo;
	}

	public Integer getAuditStatus() {
		return auditStatus;
	}

	public void setAuditStatus(Integer auditStatus) {
		this.auditStatus = auditStatus;
	}

	public String getVehicleAgeType() {
		return vehicleAgeType;
	}

	public void setVehicleAgeType(String vehicleAgeType) {
		this.vehicleAgeType = vehicleAgeType;
	}

	public String getVehicleInsurancePolicy() {
		return vehicleInsurancePolicy;
	}

	public void setVehicleInsurancePolicy(String vehicleInsurancePolicy) {
		this.vehicleInsurancePolicy = vehicleInsurancePolicy;
	}

	public String getComplianceQualificationCertificates() {
		return complianceQualificationCertificates;
	}

	public void setComplianceQualificationCertificates(String complianceQualificationCertificates) {
		this.complianceQualificationCertificates = complianceQualificationCertificates;
	}

	public String getNewOcrFieldValue() {
		return newOcrFieldValue;
	}

	public void setNewOcrFieldValue(String newOcrFieldValue) {
		this.newOcrFieldValue = newOcrFieldValue;
	}
}
