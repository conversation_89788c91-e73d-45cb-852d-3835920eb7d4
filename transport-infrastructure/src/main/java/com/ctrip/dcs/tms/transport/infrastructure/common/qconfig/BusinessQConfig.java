package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import com.ctrip.dcs.tms.transport.infrastructure.common.dto.MobileCountryRuleDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.MobileRuleDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.Feature;
import qunar.tc.qconfig.client.JsonConfig;

import java.util.Map;
import java.util.Optional;

@Component
public class BusinessQConfig {

  private final JsonConfig<MobileRuleDTO>
    config = JsonConfig.get("business.json", Feature.create().setFailOnNotExists(false).build(), MobileRuleDTO.class);

  public boolean isOpen() {
    return "ON".equals(config.current().getOpen());
  }
  public boolean isDrvPreCheckOff() {
    return "OFF".equals(config.current().getDrvPreCheck());
  }

  public MobileCountryRuleDTO getMobileCountryRuleDTO(Long countryId) {
    String mobileCountryRuleStr = config.current().getMobileCountryRuleStr();
    if (StringUtils.isEmpty(mobileCountryRuleStr)) {
      return null;
    }
    if ("OFF".equals(config.current().getOpen())) {
      return null;
    }
    return JsonUtil.fromJson(mobileCountryRuleStr, new TypeReference<Map<String, MobileCountryRuleDTO>>() {
    }).get(String.valueOf(countryId));
  }

  public boolean isCheckMobileByGooglePhoneNumberUtil() {
    return Boolean.parseBoolean(Optional.ofNullable(config.current().getCheckMobileByGooglePhoneNumberUtil()).orElse("true"));
  }
}
