package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.annotation.*;

import javax.persistence.*;
import java.sql.*;

/**
 * <AUTHOR>
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "")
public class DriverCacheInfoPO implements DalPojo {

    /**
     * 司机主键
     */
    @Id
    @Column(name = "drv_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long drvId;

    /**
     * 司机状态(0未激活、1上线、2冻结、3下线)
     */
    @Column(name = "drv_status")
    @Type(value = Types.TINYINT)
    private Integer drvStatus;

    /**
     * 合作模式。1.全职，2.兼职
     */
    @Column(name = "coop_mode")
    @Type(value = Types.TINYINT)
    private Integer coopMode;

    /**
     * 司机姓名
     */
    @Column(name = "drv_name")
    @Type(value = Types.VARCHAR)
    private String drvName;

    /**
     * 司机手机号
     */
    @Column(name = "drv_phone")
    @Type(value = Types.VARCHAR)
    private String drvPhone;

    /**
     * 国际区号，如中国国际区号是+86
     */
    @Column(name = "igt_code")
    @Type(value = Types.VARCHAR)
    private String igtCode;

    /**
     * 车辆主键
     */
    @Id
    @Column(name = "vehicle_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long vehicleId;

    /**
     * 车牌号
     */
    @Column(name = "vehicle_license")
    @Type(value = Types.VARCHAR)
    private String vehicleLicense;

    /**
     * 车型ID
     */
    @Column(name = "vehicle_type_id")
    @Type(value = Types.BIGINT)
    private Long vehicleTypeId;

    /**
     * 车身颜色ID
     */
    @Column(name = "vehicle_color_id")
    @Type(value = Types.BIGINT)
    private Long vehicleColorId;

    /**
     * 车辆品牌ID
     */
    @Column(name = "vehicle_brand_id")
    @Type(value = Types.BIGINT)
    private Long vehicleBrandId;

    /**
     * 车辆能源类型：1燃油车辆，2纯电动车辆，3油电混合车辆
     */
    @Column(name = "vehicle_energy_type")
    @Type(value = Types.TINYINT)
    private Integer vehicleEnergyType;

    /**
     * 意愿车型(车型ID,多个逗号分隔)
     */
    @Column(name = "intend_vehicle_type_id")
    @Type(value = Types.VARCHAR)
    private String intendVehicleTypeId;

    /**
     * 城市
     */
    @Column(name = "city_id")
    @Type(value = Types.BIGINT)
    private Long cityId;

    /**
     * 司机住址
     */
    @Column(name = "drv_addr")
    @Type(value = Types.VARCHAR)
    private String drvAddr;

    /**
     * 工作时段
     */
    @Column(name = "work_period")
    @Type(value = Types.VARCHAR)
    private String workPeriod;

    /**
     * 司机语言(语言ID逗号分隔)
     */
    @Column(name = "drv_language")
    @Type(value = Types.VARCHAR)
    private String drvLanguage;

    /**
     * 国家码
     */
    @Column(name = "country_id")
    @Type(value = Types.BIGINT)
    private Long countryId;

    /**
     * 国家名称
     */
    @Column(name = "country_name")
    @Type(value = Types.VARCHAR)
    private String countryName;

    /**
     * 供应商Id
     */
    @Column(name = "supplier_id")
    @Type(value = Types.BIGINT)
    private Long supplierId;

    /**
     * email
     */
    @Column(name = "email")
    @Type(value = Types.VARCHAR)
    private String email;

    /**
     * 微信
     */
    @Column(name = "wechat")
    @Type(value = Types.VARCHAR)
    private String wechat;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 是否国内。1:国内，2:国际
     */
    @Column(name = "internal_scope")
    @Type(value = Types.TINYINT)
    private Integer internalScope;

    /**
     * 头像
     */
    @Column(name = "drv_head_img")
    @Type(value = Types.VARCHAR)
    private String drvHeadImg;

    /**
     * 车系
     */
    @Column(name = "vehicle_series")
    @Type(value = Types.BIGINT)
    private Long vehicleSeries;

    /**
     * 司机的逻辑产线
     */
    @Column(name = "drv_category_synthesize_code")
    @Type(value = Types.TINYINT)
    private Integer drvCategorySynthesizeCode;

    /**
     * 车辆的逻辑产线
     */
    @Column(name = "veh_category_synthesize_code")
    @Type(value = Types.TINYINT)
    private Integer vehCategorySynthesizeCode;

    /**
     * 车辆状态  0.未激活,1.上线.3.下线
     */
    @Column(name = "vehicle_status")
    @Type(value = Types.TINYINT)
    private Integer vehicleStatus;

    /**
     * 身份证号(密文)
     */
    @Column(name = "drv_idcard")
    @Type(value = Types.VARCHAR)
    private String drvIdcard;

    /**
     * 初次领证日期
     */
    @Column(name = "certi_date")
    @Type(value = Types.DATE)
    private Date certiDate;

    /**
     * 驾驶证有效期限开始时间
     */
    @Column(name = "expiry_begin_date")
    @Type(value = Types.DATE)
    private Date expiryBeginDate;

    /**
     * 驾驶证有效期限结束时间
     */
    @Column(name = "expiry_end_date")
    @Type(value = Types.DATE)
    private Date expiryEndDate;
    /**
     * 驾驶证号
     */
    @Column(name = "drv_license_number")
    @Type(value = Types.VARCHAR)
    private String drvLicenseNumber;

    /**
     * vin码
     */
    @Column(name = "vin")
    @Type(value = Types.VARCHAR)
    private String vin;
    /**
     * 上线时间
     */
    @Column(name = "online_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp onlineTime;
    /**
     * 车辆注册时间
     */
    @Column(name = "regst_date")
    @Type(value = Types.TIMESTAMP)
    private Timestamp vehRegstDate;
    /**
     * 车辆创建时间
     */
    @Column(name = "veh_create_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp vehCreateTime;

    /**
     * 上线时间
     */
    @Column(name = "veh_bind_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp vehBindTime;

    /**
     * 驾驶证图片地址
     */
    @Column(name = "drvcard_img")
    @Type(value = Types.VARCHAR)
    private String drvcardImg;

    /**
     * 车身照片
     */
    @Column(name = "vehicle_full_img")
    @Type(value = Types.VARCHAR)
    private String vehicleFullImg;

    /**
     * 司机民族
     */
    @Column(name = "nation")
    @Type(value = Types.VARCHAR)
    private String nation;

    /**
     * 举牌接机服务(0.否,1.是)
     */
    @Column(name = "raising_pick_up")
    @Type(value = Types.BIT)
    private Boolean raisingPickUp;

    /**
     * 儿童座椅服务(0.否,1.是)
     */
    @Column(name = "child_seat")
    @Type(value = Types.BIT)
    private Boolean childSeat;

    /**
     * 网约车驾驶证号
     */
    @Column(name = "driver_net_cert_no")
    @Type(value = Types.VARCHAR)
    private String driverNetCertNo;

    /**
     * 网约车运输证号
     */
    @Column(name = "vehicle_net_cert_no")
    @Type(value = Types.VARCHAR)
    private String vehicleNetCertNo;

    public Boolean getRaisingPickUp() {
        return raisingPickUp;
    }

    public void setRaisingPickUp(Boolean raisingPickUp) {
        this.raisingPickUp = raisingPickUp;
    }

    public Boolean getChildSeat() {
        return childSeat;
    }

    public void setChildSeat(Boolean childSeat) {
        this.childSeat = childSeat;
    }

    public Timestamp getVehBindTime() {
        return vehBindTime;
    }

    public void setVehBindTime(Timestamp vehBindTime) {
        this.vehBindTime = vehBindTime;
    }

    public Timestamp getOnlineTime() {
        return onlineTime;
    }

    public void setOnlineTime(Timestamp onlineTime) {
        this.onlineTime = onlineTime;
    }

    public Integer getVehicleStatus() {
        return vehicleStatus;
    }

    public void setVehicleStatus(Integer vehicleStatus) {
        this.vehicleStatus = vehicleStatus;
    }

    public Integer getDrvCategorySynthesizeCode() {
        return drvCategorySynthesizeCode;
    }

    public void setDrvCategorySynthesizeCode(Integer drvCategorySynthesizeCode) {
        this.drvCategorySynthesizeCode = drvCategorySynthesizeCode;
    }

    public Integer getVehCategorySynthesizeCode() {
        return vehCategorySynthesizeCode;
    }

    public void setVehCategorySynthesizeCode(Integer vehCategorySynthesizeCode) {
        this.vehCategorySynthesizeCode = vehCategorySynthesizeCode;
    }

    public Long getVehicleSeries() {
        return vehicleSeries;
    }

    public void setVehicleSeries(Long vehicleSeries) {
        this.vehicleSeries = vehicleSeries;
    }

    public String getDrvHeadImg() {
        return drvHeadImg;
    }

    public void setDrvHeadImg(String drvHeadImg) {
        this.drvHeadImg = drvHeadImg;
    }

    public Integer getInternalScope() {
        return internalScope;
    }

    public void setInternalScope(Integer internalScope) {
        this.internalScope = internalScope;
    }

    public Long getDrvId() {
        return drvId;
    }

    public void setDrvId(Long drvId) {
        this.drvId = drvId;
    }

    public Integer getDrvStatus() {
        return drvStatus;
    }

    public void setDrvStatus(Integer drvStatus) {
        this.drvStatus = drvStatus;
    }

    public Integer getCoopMode() {
        return coopMode;
    }

    public void setCoopMode(Integer coopMode) {
        this.coopMode = coopMode;
    }

    public String getDrvName() {
        return drvName;
    }

    public void setDrvName(String drvName) {
        this.drvName = drvName;
    }

    public String getDrvPhone() {
        return drvPhone;
    }

    public void setDrvPhone(String drvPhone) {
        this.drvPhone = drvPhone;
    }

    public String getIgtCode() {
        return igtCode;
    }

    public void setIgtCode(String igtCode) {
        this.igtCode = igtCode;
    }

    public Long getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(Long vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getVehicleLicense() {
        return vehicleLicense;
    }

    public void setVehicleLicense(String vehicleLicense) {
        this.vehicleLicense = vehicleLicense;
    }

    public Long getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(Long vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public Long getVehicleColorId() {
        return vehicleColorId;
    }

    public void setVehicleColorId(Long vehicleColorId) {
        this.vehicleColorId = vehicleColorId;
    }

    public Long getVehicleBrandId() {
        return vehicleBrandId;
    }

    public void setVehicleBrandId(Long vehicleBrandId) {
        this.vehicleBrandId = vehicleBrandId;
    }

    public Integer getVehicleEnergyType() {
        return vehicleEnergyType;
    }

    public void setVehicleEnergyType(Integer vehicleEnergyType) {
        this.vehicleEnergyType = vehicleEnergyType;
    }

    public String getIntendVehicleTypeId() {
        return intendVehicleTypeId;
    }

    public void setIntendVehicleTypeId(String intendVehicleTypeId) {
        this.intendVehicleTypeId = intendVehicleTypeId;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public String getDrvAddr() {
        return drvAddr;
    }

    public void setDrvAddr(String drvAddr) {
        this.drvAddr = drvAddr;
    }

    public String getWorkPeriod() {
        return workPeriod;
    }

    public void setWorkPeriod(String workPeriod) {
        this.workPeriod = workPeriod;
    }

    public String getDrvLanguage() {
        return drvLanguage;
    }

    public void setDrvLanguage(String drvLanguage) {
        this.drvLanguage = drvLanguage;
    }

    public Long getCountryId() {
        return countryId;
    }

    public void setCountryId(Long countryId) {
        this.countryId = countryId;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public String getDrvIdcard() {
        return drvIdcard;
    }

    public void setDrvIdcard(String drvIdcard) {
        this.drvIdcard = drvIdcard;
    }

    public Date getCertiDate() {
        return certiDate;
    }

    public void setCertiDate(Date certiDate) {
        this.certiDate = certiDate;
    }

    public Date getExpiryBeginDate() {
        return expiryBeginDate;
    }

    public void setExpiryBeginDate(Date expiryBeginDate) {
        this.expiryBeginDate = expiryBeginDate;
    }

    public Date getExpiryEndDate() {
        return expiryEndDate;
    }

    public void setExpiryEndDate(Date expiryEndDate) {
        this.expiryEndDate = expiryEndDate;
    }

    public String getDrvLicenseNumber() {
        return drvLicenseNumber;
    }

    public void setDrvLicenseNumber(String drvLicenseNumber) {
        this.drvLicenseNumber = drvLicenseNumber;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public Timestamp getVehRegstDate() {
        return vehRegstDate;
    }

    public void setVehRegstDate(Timestamp vehRegstDate) {
        this.vehRegstDate = vehRegstDate;
    }

    public Timestamp getVehCreateTime() {
        return vehCreateTime;
    }

    public void setVehCreateTime(Timestamp vehCreateTime) {
        this.vehCreateTime = vehCreateTime;
    }

    public String getDrvcardImg() {
        return drvcardImg;
    }

    public void setDrvcardImg(String drvcardImg) {
        this.drvcardImg = drvcardImg;
    }

    public String getVehicleFullImg() {
        return vehicleFullImg;
    }

    public void setVehicleFullImg(String vehicleFullImg) {
        this.vehicleFullImg = vehicleFullImg;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getDriverNetCertNo() {
        return driverNetCertNo;
    }

    public void setDriverNetCertNo(String driverNetCertNo) {
        this.driverNetCertNo = driverNetCertNo;
    }

    public String getVehicleNetCertNo() {
        return vehicleNetCertNo;
    }

    public void setVehicleNetCertNo(String vehicleNetCertNo) {
        this.vehicleNetCertNo = vehicleNetCertNo;
    }
}