package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc;

import com.ctrip.dcs.self.dispatchorder.client.SelfDispatchOrderServiceClient;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryDriverOrCarInServiceOrderRequestType;
import com.ctrip.dcs.self.dispatchorder.interfaces.QueryDriverOrCarInServiceOrderResponseType;
import com.ctrip.igt.framework.soa.client.ServiceClient;

@ServiceClient(value = SelfDispatchOrderServiceClient.class,format = "json")
public interface SelfDispatchOrderService {
    public QueryDriverOrCarInServiceOrderResponseType queryDriverOrCarInServiceOrderCount(QueryDriverOrCarInServiceOrderRequestType request);
}
