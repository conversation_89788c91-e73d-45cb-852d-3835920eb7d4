package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.http;

import com.ctrip.dcs.dsp.driver.level.api.BatchDriverPointsQry;
import com.ctrip.dcs.dsp.driver.level.api.BatchDriverPointsResp;
import com.ctrip.dcs.dsp.driver.level.api.DriverServicePointsDto;
import com.ctrip.dcs.tms.transport.api.model.QueryBingIngSkuSOADTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DcsDriverLevelServiceClientProxy;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.common.base.PageHolder;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctriposs.baiji.exception.*;
import com.fasterxml.jackson.core.type.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import okhttp3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import org.springframework.util.*;

import java.io.*;
import java.util.Objects;
import java.util.*;
import java.util.stream.*;

/**
 * 司机分查询
 */
@Service
public class DriverPointsQueryProxy {
    @Autowired
    DcsDriverLevelServiceClientProxy driverLevelServiceClientProxy;

    /**
     * 查询司机城市排名
     * @param drvIds
     * @param cityid
     * @return
     */
    public Result<Map<Long,List<DriverPoint>>> queryDrvCityRanking(String drvIds, Long cityid) {
        Result.Builder<Map<Long,List<DriverPoint>>> modelBuilder = Result.Builder.<Map<Long,List<DriverPoint>>>newResult();
        if (Strings.isNullOrEmpty(drvIds) || Objects.isNull(cityid)) {
            return modelBuilder.success().withData(Maps.newHashMap()).build();
        }
        return queryDrvCityRankingFromC(drvIds,cityid);
    }

    public List<DriverPoint> queryDriverPointList(String drvIds, Long cityid){
        if (Strings.isNullOrEmpty(drvIds) || Objects.isNull(cityid)) {
            return Collections.emptyList();
        }
        return queryDriverPointListFromC(drvIds,cityid);
    }

    //查询C侧司机分
    public Result<Map<Long,List<DriverPoint>>> queryDrvCityRankingFromC(String drvIds, Long cityid){
        try {
            List<DriverPoint> driverPointList = queryDriverPointListFromC(drvIds,cityid);
            if(CollectionUtils.isEmpty(driverPointList)){
                return Result.Builder.<Map<Long,List<DriverPoint>>>newResult().success().withData(Maps.newHashMap()).build();
            }
            Map<Long, List<DriverPoint>> collect = driverPointList.stream().collect(Collectors.groupingBy(DriverPoint::getCityRanking));
            return Result.Builder.<Map<Long,List<DriverPoint>>>newResult().success().withData(collect).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    //查询C侧司机分
    public List<DriverPoint> queryDriverPointListFromC(String drvIds, Long cityid) {
        try {
            List<DriverServicePointsDto> pointsDtos = requestDriverPoints(drvIds, cityid);
            if (CollectionUtils.isEmpty(pointsDtos)) {
                return Collections.emptyList();
            }
            List<DriverPoint> driverPointList = Lists.newArrayList();
            for (DriverServicePointsDto pointsDto : pointsDtos) {
                if (pointsDto.getCityRanking() == null) {
                    continue;
                }
                DriverPoint driverPoint = new DriverPoint();
                driverPoint.setCityRanking(pointsDto.getCityRanking());
                driverPoint.setDriverId(pointsDto.getDriverId().longValue());
                driverPoint.setPoints(pointsDto.getPoints());
                driverPointList.add(driverPoint);
            }

            return driverPointList;
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    public List<DriverServicePointsDto> requestDriverPoints(String drvIds, Long cityid){
        try {
            BatchDriverPointsQry pointsQry = new BatchDriverPointsQry();
            pointsQry.setCityId(cityid);
            pointsQry.setDriverIdList(drvIds);
            BatchDriverPointsResp responsePointsResp =  driverLevelServiceClientProxy.queryDriverBatchPoints(pointsQry);
            if (responsePointsResp == null || !Objects.equals(responsePointsResp.responseResult.getReturnCode(), TmsTransportConstant.ResultStatusTypeEnum.SUCCESS_CODE.getCode())) {
                return Collections.emptyList();
            }
            return responsePointsResp.getDtos();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }
}
