package com.ctrip.dcs.tms.transport.infrastructure.gateway;

import com.ctrip.basebiz.callcenter.splitservice.contract.BatchSplitNumberRequestType;
import com.ctrip.basebiz.callcenter.splitservice.contract.BatchSplitNumberResponseType;
import com.ctrip.basebiz.callcenter.splitservice.contract.NumberDTO;
import com.ctrip.basebiz.callcenter.splitservice.contract.SplitNumberResponseType;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.PhoneNumberSplitServiceProxy;
import org.apache.commons.collections.MapUtils;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2023/7/3 17:42
 */
@RunWith(MockitoJUnitRunner.class)
public class PhoneNumberServiceGatewayTest {

    @Mock
    private BatchSplitNumberRequestType req;

    @Mock
    private BatchSplitNumberResponseType response;

    @Mock
    private PhoneNumberSplitServiceProxy phoneNumberSplitService;

    @InjectMocks
    private PhoneNumberServiceGateway phoneNumberSplitServiceProxy;

    @Test
    public void testBatchSplitNumber() {
        Map<String, NumberDTO> results = new HashMap<>();
        results.put("1234567890", new NumberDTO());
        Mockito.when(response.getResults()).thenReturn(results);
        Mockito.when(req.getNumbers()).thenReturn(Lists.newArrayList("1"));
        Mockito.when(phoneNumberSplitService.batchSplitNumber(req)).thenReturn(response);
        Map<String, NumberDTO> actualResults = phoneNumberSplitServiceProxy.batchSplitNumber(req);
        Assert.assertEquals(results, actualResults);
    }

    @Test
    public void testBatchSplitNumberFail() {
        Map<String, NumberDTO> actualResults = phoneNumberSplitServiceProxy.batchSplitNumber(req);
        Assert.assertTrue(MapUtils.isEmpty(actualResults));
    }

    @Test
    public void testSplitNumber() {
        SplitNumberResponseType responseType = new SplitNumberResponseType();
        responseType.setNumber("123456789");
        NumberDTO numberDTO = new NumberDTO();
        numberDTO.setBodyNumber("123456789");
        responseType.setResult(numberDTO);
        Mockito.when(phoneNumberSplitService.splitNumber(Mockito.any())).thenReturn(responseType);
        Optional<NumberDTO> actualResults = phoneNumberSplitServiceProxy.splitPhoneNumber("CN", "123456789");
        Assert.assertTrue(actualResults.isPresent());
    }
}
