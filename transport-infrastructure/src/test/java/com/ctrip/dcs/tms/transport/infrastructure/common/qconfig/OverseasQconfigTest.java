package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;


@RunWith(MockitoJUnitRunner.class)
public class OverseasQconfigTest {

    @InjectMocks
    OverseasQconfig qconfig;

    @Test
    public void getOverseasGrayScaleSupplierConf() {
        String str = qconfig.getOverseasGrayScaleSupplierConf();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setOverseasGrayScaleSupplierConf() {
        qconfig.setOverseasGrayScaleSupplierConf("11");
        Assert.assertTrue(true);
    }

    @Test
    public void getOverseasOcrTotalSwitch() {
        Boolean str = qconfig.getOverseasOcrTotalSwitch();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setOverseasOcrTotalSwitch() {
        qconfig.setOverseasOcrTotalSwitch(Boolean.TRUE);
        Assert.assertTrue(true);
    }

    @Test
    public void getOverseasOcrVerifyConf() {
        String str = qconfig.getOverseasOcrVerifyConf();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setOverseasOcrVerifyConf() {
        qconfig.setOverseasOcrVerifyConf("111");
        Assert.assertTrue(true);
    }

    @Test
    public void getOverseasOcrTimeoutThreshold() {
        Integer threshold = qconfig.getOverseasOcrTimeoutThreshold();
        Assert.assertTrue(threshold == null);
    }

    @Test
    public void setOverseasOcrTimeoutThreshold() {
        qconfig.setOverseasOcrTimeoutThreshold(30);
        Assert.assertTrue(true);
    }

    @Test
    public void setVehicleUnactCountryConf() {
        qconfig.setVehicleUnactCountryConf("30");
        String str = qconfig.getVehicleUnactCountryConf();
        Assert.assertTrue("30".equals(str));
    }

    @Test
    public void setFiltrationServiceproviderid() {
        qconfig.setFiltrationServiceproviderid("30");
        String str = qconfig.getFiltrationServiceproviderid();
        Assert.assertTrue("30".equals(str));
    }
}
