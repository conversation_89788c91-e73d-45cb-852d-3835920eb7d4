package com.ctrip.dcs.tms.transport.infrastructure.common.aspect;

import org.aspectj.lang.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class RestSessionAspectTest {

    @InjectMocks
    private RestSessionAspect restSessionAspect;

    @Test
    public void test() throws Throwable {
        try {
            restSessionAspect.keepSession(null);
        } catch (Throwable throwable) {
        }
    }

}