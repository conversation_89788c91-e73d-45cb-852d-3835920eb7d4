package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class TransportCommonQconfigTest {

  private TransportCommonQconfig transportCommonQconfigUnderTest;

  @Before
  public void setUp() throws Exception {
    transportCommonQconfigUnderTest = new TransportCommonQconfig();
    transportCommonQconfigUnderTest.setRegisterDriverAccountSwitch("registerDriverAccountSwitch");
    transportCommonQconfigUnderTest.setRegisterDriverAccountWithNewInterfaceSwitch(
      "registerDriverAccountWithNewInterfaceSwitch");
  }

  @Test
  public void testGetRegisterDriverAccountWithNewInterfaceSwitch() {
    assertThat(transportCommonQconfigUnderTest.getRegisterDriverAccountWithNewInterfaceSwitch()).isEqualTo("registerDriverAccountWithNewInterfaceSwitch");
  }

  @Test
  public void testQmqOrderMessageSwitchGetterAndSetter() {
    final String qmqOrderMessageSwitch = "qmqOrderMessageSwitch";
    transportCommonQconfigUnderTest.setQmqOrderMessageSwitch(qmqOrderMessageSwitch);
    assertThat(transportCommonQconfigUnderTest.getQmqOrderMessageSwitch()).isEqualTo(qmqOrderMessageSwitch);
  }

  @Test
  public void testDriverPwdTokenGetterAndSetter() {
    final String driverPwdToken = "driverPwdToken";
    transportCommonQconfigUnderTest.setDriverPwdToken(driverPwdToken);
    assertThat(transportCommonQconfigUnderTest.getDriverPwdToken()).isEqualTo(driverPwdToken);
  }

  @Test
  public void testGetRegisterDriverAccountSwitch() {
    assertThat(transportCommonQconfigUnderTest.getRegisterDriverAccountSwitch()).isEqualTo("registerDriverAccountSwitch");
  }
}
