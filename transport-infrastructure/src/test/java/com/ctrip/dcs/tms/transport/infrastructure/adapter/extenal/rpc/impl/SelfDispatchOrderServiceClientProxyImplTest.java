package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.impl;

import com.ctrip.dcs.self.dispatchorder.interfaces.QueryDriverOrCarInServiceOrderResponseType;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.SelfDispatchOrderService;
import com.ctrip.igt.ResponseResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SelfDispatchOrderServiceClientProxyImplTest {
    @InjectMocks
    SelfDispatchOrderServiceClientProxyImpl proxy;
    @Mock
    private SelfDispatchOrderService service;
    @Test
    public void hasWaitingServiceOrderForDriver(){
        QueryDriverOrCarInServiceOrderResponseType responseType = new QueryDriverOrCarInServiceOrderResponseType();
        Mockito.when(service.queryDriverOrCarInServiceOrderCount(Mockito.any())).thenReturn(responseType);
        boolean result = proxy.hasWaitingServiceOrderForDriver(2L);
        Assert.assertTrue(result);

        ResponseResult responseResult = new ResponseResult();
        responseResult.setSuccess(true);
        responseType.setResponseResult(responseResult);
        result = proxy.hasWaitingServiceOrderForDriver(2L);
        Assert.assertTrue(!result);

        responseType.setOrderNum(2);
        result = proxy.hasWaitingServiceOrderForDriver(2L);
        Assert.assertTrue(result);
    }

    @Test
    public void hasWaitingServiceOrderForVehicle(){
        QueryDriverOrCarInServiceOrderResponseType responseType = new QueryDriverOrCarInServiceOrderResponseType();
        Mockito.when(service.queryDriverOrCarInServiceOrderCount(Mockito.any())).thenReturn(responseType);
        boolean result = proxy.hasWaitingServiceOrderForVehicle(2L);
        Assert.assertTrue(result);

        ResponseResult responseResult = new ResponseResult();
        responseResult.setSuccess(true);
        responseType.setResponseResult(responseResult);
        result = proxy.hasWaitingServiceOrderForVehicle(2L);
        Assert.assertTrue(!result);

        responseType.setOrderNum(2);
        result = proxy.hasWaitingServiceOrderForVehicle(2L);
        Assert.assertTrue(result);
    }
}
