package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.ctrip.dcs.phonebridge.service.interfaces.message.BatchIdentificationRequestType;
import com.ctrip.dcs.phonebridge.service.interfaces.message.BatchIdentificationResponseType;
import com.ctrip.dcs.phonebridge.service.interfaces.message.dto.IdentificationParamDTO;
import com.ctrip.dcs.phonebridge.service.interfaces.message.dto.PhotoDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.PhoneBridgeService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.messaging.qmq.TmsQmqProducer;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DrvMobileNumberAuthDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DrvMobileNumberAuthReqDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.common.result.Result;

import java.util.ArrayList;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class PhoneBridgeServiceProxyImplTest {

    @InjectMocks
    private PhoneBridgeServiceProxyImpl phoneBridgeServiceProxy;

    @Mock
    private PhoneBridgeService phoneBridgeService;
    
    @Mock
    private CommonConfig config;
    
    @Mock
    private TmsQmqProducer tmsQmqProducer;

    private DrvMobileNumberAuthReqDTO reqDTO;
    private BatchIdentificationResponseType successResponse;
    private BatchIdentificationResponseType failResponse;

    @Before
    public void setUp() throws Exception {
        // 准备测试数据
        reqDTO = new DrvMobileNumberAuthReqDTO();
        reqDTO.setDriverId(12345L);
        reqDTO.setDriverName("测试司机");
        reqDTO.setMobilePhone("13800138000");
        reqDTO.setIgtCode("86");
        reqDTO.setIdCard("110101199001011234");
        reqDTO.setIdCardImgUrl("http://example.com/idcard_front.jpg");
        reqDTO.setIdCardBackImgUrl("http://example.com/idcard_back.jpg");
        reqDTO.setScenePhotoUrl("http://example.com/scene_photo.jpg");

        // 成功响应
        successResponse = new BatchIdentificationResponseType();
        ResponseResult successResult = new ResponseResult();
        successResult.setReturnCode("200");
        successResult.setSuccess(true);
        successResponse.setResponseResult(successResult);

        // 失败响应
        failResponse = new BatchIdentificationResponseType();
        ResponseResult failResult = new ResponseResult();
        failResult.setReturnCode("Fail");
        failResult.setReturnMessage("认证失败");
        failResult.setSuccess(false);
        failResponse.setResponseResult(failResult);
        
        // 配置Mock
        when(config.getRealNameAuthSwitch()).thenReturn(false);
        when(config.getFilterHeadImg()).thenReturn(new ArrayList<>());
    }

    @Test
    public void testBatchIdentification_Success() throws Exception {
        // 设置mock行为
        when(phoneBridgeService.batchIdentification(any())).thenReturn(successResponse);

        // 调用被测方法
        Result<DrvMobileNumberAuthDTO> result = phoneBridgeServiceProxy.batchIdentification(reqDTO);

        // 验证结果
        Assert.assertTrue(result.isSuccess());

        // 验证请求参数
        ArgumentCaptor<BatchIdentificationRequestType> requestCaptor = ArgumentCaptor.forClass(BatchIdentificationRequestType.class);
        verify(phoneBridgeService).batchIdentification(requestCaptor.capture());
        
        BatchIdentificationRequestType request = requestCaptor.getValue();
        List<IdentificationParamDTO> params = request.getIdentificationParams();
        Assert.assertNotNull(params);
        Assert.assertEquals(1, params.size());
        
        IdentificationParamDTO param = params.get(0);
        Assert.assertEquals(reqDTO.getDriverName(), param.getPersonName());
        Assert.assertEquals(reqDTO.getMobilePhone(), param.getPhoneNumber());
        Assert.assertEquals(reqDTO.getIdCard(), param.getIdNumber());
        Assert.assertEquals("18000013", param.getBusinessId());
        Assert.assertEquals("100025330", param.getAppId());
    }

    @Test
    public void testBatchIdentification_Failure() throws Exception {
        // 设置mock行为
        when(phoneBridgeService.batchIdentification(any())).thenReturn(failResponse);

        // 调用被测方法
        Result<DrvMobileNumberAuthDTO> result = phoneBridgeServiceProxy.batchIdentification(reqDTO);

        // 验证结果
        Assert.assertFalse(result.isSuccess());
        Assert.assertEquals("Fail", result.getCode());
        Assert.assertEquals("认证失败", result.getMsg());
    }

    @Test
    public void testPhotoBuilding() throws Exception {
        // 设置mock行为
        when(phoneBridgeService.batchIdentification(any())).thenReturn(successResponse);

        // 调用被测方法
        phoneBridgeServiceProxy.batchIdentification(reqDTO);

        // 验证请求参数
        ArgumentCaptor<BatchIdentificationRequestType> requestCaptor = ArgumentCaptor.forClass(BatchIdentificationRequestType.class);
        verify(phoneBridgeService).batchIdentification(requestCaptor.capture());
        
        BatchIdentificationRequestType request = requestCaptor.getValue();
        List<IdentificationParamDTO> params = request.getIdentificationParams();
        IdentificationParamDTO param = params.get(0);
        
        // 验证照片URL正确设置
        PhotoDTO idPhotoFront = param.getIdPhotoFront();
        PhotoDTO idPhotoBack = param.getIdPhotoBack();
        PhotoDTO scenePhoto = param.getScenePhoto();
        
        Assert.assertNotNull(idPhotoFront);
        Assert.assertNotNull(idPhotoBack);
        Assert.assertNotNull(scenePhoto);
        
        Assert.assertEquals(reqDTO.getIdCardImgUrl(), idPhotoFront.getUrl());
        Assert.assertEquals(reqDTO.getIdCardBackImgUrl(), idPhotoBack.getUrl());
        Assert.assertEquals(reqDTO.getScenePhotoUrl(), scenePhoto.getUrl());
    }

    @Test
    public void testNumberTypeLogic() throws Exception {
        // 设置mock行为
        when(phoneBridgeService.batchIdentification(any())).thenReturn(successResponse);

        // 调用被测方法，使用中国区号
        reqDTO.setIgtCode("86");
        phoneBridgeServiceProxy.batchIdentification(reqDTO);

        // 验证请求参数
        ArgumentCaptor<BatchIdentificationRequestType> requestCaptor = ArgumentCaptor.forClass(BatchIdentificationRequestType.class);
        verify(phoneBridgeService).batchIdentification(requestCaptor.capture());
        
        BatchIdentificationRequestType request = requestCaptor.getValue();
        List<IdentificationParamDTO> params = request.getIdentificationParams();
        IdentificationParamDTO param = params.get(0);
        
        // 验证号码类型和人员类型
        Assert.assertEquals(Integer.valueOf(0), param.getNumberType()); // MOBILE
        Assert.assertEquals(Integer.valueOf(0), param.getPersonnelType()); // PERSONAL
    }
    
    @Test
    public void testSendAsyncMethod() throws Exception {
        // 设置mock行为，启用异步模式
        when(config.getRealNameAuthSwitch()).thenReturn(true);
        
        // 调用被测方法
        Result<DrvMobileNumberAuthDTO> result = phoneBridgeServiceProxy.batchIdentification(reqDTO);
        
        // 验证结果
        Assert.assertTrue(result.isSuccess());
        
        // 验证消息发送
        ArgumentCaptor<Map<String, Object>> messageCaptor = ArgumentCaptor.forClass(Map.class);
        verify(tmsQmqProducer).sendMessage(
            eq(TmsTransportConstant.QmqSubject.DCS_VPHONE_IDENTIFICATION_ASYNC_NOTIFY),
            messageCaptor.capture(), 
            eq(3)
        );
        
        // 验证消息内容
        Map<String, Object> capturedMessage = messageCaptor.getValue();
        Assert.assertNotNull(capturedMessage);
        Assert.assertTrue(capturedMessage.containsKey("param"));
        String paramJson = (String) capturedMessage.get("param");
        Assert.assertNotNull(paramJson);
        Assert.assertTrue(paramJson.contains(reqDTO.getDriverName()));
        Assert.assertTrue(paramJson.contains(reqDTO.getMobilePhone()));
        Assert.assertTrue(paramJson.contains(reqDTO.getIdCard()));
    }
} 