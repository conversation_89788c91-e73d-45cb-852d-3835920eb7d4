package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DriverGroupRelationPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvVehLegendPO;
import com.ctrip.igt.framework.dal.DalRepositoryImpl;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.DalQueryDao;
import com.ctrip.platform.dal.dao.DalTableDao;
import com.ctrip.platform.dal.dao.StatementParameters;
import com.ctrip.platform.dal.dao.helper.DalDefaultJpaMapper;
import com.ctrip.platform.dal.dao.sqlbuilder.FreeSelectSqlBuilder;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DalQueryDao.class, DrvVehLegendRepositoryImpl.class, DalTableDao.class})
@PowerMockIgnore({"javax.management.*","javax.crypto.*"})
public class DrvVehLegendRepositoryImplTest {



    @Test
    public void queryDrvVehLegendList() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvVehLegendRepositoryImpl relationRepository = new DrvVehLegendRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeSelectSqlBuilder builder = PowerMockito.mock(FreeSelectSqlBuilder.class);
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getQueryDao().query(builder,parameters,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        List<DrvVehLegendPO> list = relationRepository.queryDrvVehLegendList();
        Assert.assertTrue(list.isEmpty());
    }
}
