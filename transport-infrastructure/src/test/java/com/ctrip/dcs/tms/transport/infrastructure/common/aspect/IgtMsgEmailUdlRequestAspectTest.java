package com.ctrip.dcs.tms.transport.infrastructure.common.aspect;

import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/03 16:47
 * @Description: 单测
 */
@RunWith(MockitoJUnitRunner.class)
public class IgtMsgEmailUdlRequestAspectTest {

    @InjectMocks
    private IgtMsgEmailUdlRequestAspect emailUdlRequestAspect;

    @Test
    public void test() throws Throwable {
        Assertions.assertThatThrownBy(() -> emailUdlRequestAspect.igtMsgEmailUdl(null)).isInstanceOf(NullPointerException.class);
    }
}