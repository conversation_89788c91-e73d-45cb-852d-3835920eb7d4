//package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;
//
//import com.ctrip.dcs.tms.transport.infrastructure.common.dto.NetCertCheckRuleDTO;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.internal.util.reflection.FieldSetter;
//import org.mockito.junit.MockitoJUnitRunner;
//
//import java.lang.reflect.Field;
//
//@RunWith(MockitoJUnitRunner.class)
//public class DriverVehicleNetCertCheckRuleConfigTest {
//    @InjectMocks
//    DriverVehicleNetCertCheckRuleConfig driverVehicleNetCertCheckRuleConfig;
//    @Test
//    public void testCityIdNull(){
//        NetCertCheckRuleDTO netCertCheckRuleDTO = driverVehicleNetCertCheckRuleConfig.getRuleByCity(null);
//        Assert.assertTrue(netCertCheckRuleDTO == null);
//        NetCertCheckRuleDTO netCertCheckRuleDTO2 = driverVehicleNetCertCheckRuleConfig.getRuleByCity("32");
//        Assert.assertTrue(netCertCheckRuleDTO2 == null);
//    }
//    @Test
//    public void testCheckRuleConfigNull()throws Exception{
//        Field field = DriverVehicleNetCertCheckRuleConfig.class.getDeclaredField("checkRuleConfig");
//        FieldSetter.setField(driverVehicleNetCertCheckRuleConfig,field,"{}");
//        NetCertCheckRuleDTO netCertCheckRuleDTO2 = driverVehicleNetCertCheckRuleConfig.getRuleByCity("32");
//        Assert.assertTrue(netCertCheckRuleDTO2 == null);
//    }
//
//    @Test
//    public void testCityRuleNull()throws Exception{
//        Field field = DriverVehicleNetCertCheckRuleConfig.class.getDeclaredField("checkRuleConfig");
//        FieldSetter.setField(driverVehicleNetCertCheckRuleConfig,field,"{\"32\":{\"vehicleNetCertNoCheckRule\":[{\"totalLength\":\"7\",\"prefix\":\"0\",\"suffixType\":\"NUMBER\",\"suffixLength\":\"6\"}],\"driverNetCertNoCheckRule\":[{\"totalLength\":\"18\",\"prefix\":\"\",\"suffixType\":\"\",\"suffixLength\":\"\"},{\"totalLength\":\"15\",\"prefix\":\"\",\"suffixType\":\"\",\"suffixLength\":\"\"}],\"vehicleNetCertNoExample\":\"https://dimg04.c-ctrip.com/images/0412r120008x89sk55505_W_300_0.png\",\"driverNetCertNoExample\":\"\"},\"shen_zhen\":{\"vehicleNetCertNoCheckRule\":[{\"totalLength\":\"9\",\"prefix\":\"900\",\"suffixType\":\"NUMBER\",\"suffixLength\":\"6\"}],\"driverNetCertNoCheckRule\":[{\"totalLength\":\"10\",\"prefix\":\"9000\",\"suffixType\":\"NUMBER\",\"suffixLength\":\"6\"}],\"vehicleNetCertNoExample\":\"https://dimg04.c-ctrip.com/images/0412r120008x89sk55505_W_300_0.png\",\"driverNetCertNoExample\":\"https://dimg04.c-ctrip.com/images/0412r120008x89sk55505_W_300_0.png\"},\"san_ya\":{\"vehicleNetCertNoCheckRule\":[{\"totalLength\":\"12\",\"prefix\":\"460200\",\"suffixType\":\"NUMBER\",\"suffixLength\":\"6\"}],\"driverNetCertNoCheckRule\":[{\"totalLength\":\"18\",\"prefix\":\"\",\"suffixType\":\"\",\"suffixLength\":\"\"},{\"totalLength\":\"15\",\"prefix\":\"\",\"suffixType\":\"\",\"suffixLength\":\"\"}],\"vehicleNetCertNoExample\":\"https://dimg04.c-ctrip.com/images/0412r120008x89sk55505_W_300_0.png\",\"driverNetCertNoExample\":\"\"}}");
//        NetCertCheckRuleDTO netCertCheckRuleDTO2 = driverVehicleNetCertCheckRuleConfig.getRuleByCity("2");
//        Assert.assertTrue(netCertCheckRuleDTO2 == null);
//    }
//    @Test
//    public void testOK()throws Exception{
//        Field field = DriverVehicleNetCertCheckRuleConfig.class.getDeclaredField("checkRuleConfig");
//        FieldSetter.setField(driverVehicleNetCertCheckRuleConfig,field,"{\"32\":{\"vehicleNetCertNoCheckRule\":[{\"totalLength\":\"7\",\"prefix\":\"0\",\"suffixType\":\"NUMBER\",\"suffixLength\":\"6\"}],\"driverNetCertNoCheckRule\":[{\"totalLength\":\"18\",\"prefix\":\"\",\"suffixType\":\"\",\"suffixLength\":\"\"},{\"totalLength\":\"15\",\"prefix\":\"\",\"suffixType\":\"\",\"suffixLength\":\"\"}],\"vehicleNetCertNoExample\":\"https://dimg04.c-ctrip.com/images/0412r120008x89sk55505_W_300_0.png\",\"driverNetCertNoExample\":\"\"},\"shen_zhen\":{\"vehicleNetCertNoCheckRule\":[{\"totalLength\":\"9\",\"prefix\":\"900\",\"suffixType\":\"NUMBER\",\"suffixLength\":\"6\"}],\"driverNetCertNoCheckRule\":[{\"totalLength\":\"10\",\"prefix\":\"9000\",\"suffixType\":\"NUMBER\",\"suffixLength\":\"6\"}],\"vehicleNetCertNoExample\":\"https://dimg04.c-ctrip.com/images/0412r120008x89sk55505_W_300_0.png\",\"driverNetCertNoExample\":\"https://dimg04.c-ctrip.com/images/0412r120008x89sk55505_W_300_0.png\"},\"san_ya\":{\"vehicleNetCertNoCheckRule\":[{\"totalLength\":\"12\",\"prefix\":\"460200\",\"suffixType\":\"NUMBER\",\"suffixLength\":\"6\"}],\"driverNetCertNoCheckRule\":[{\"totalLength\":\"18\",\"prefix\":\"\",\"suffixType\":\"\",\"suffixLength\":\"\"},{\"totalLength\":\"15\",\"prefix\":\"\",\"suffixType\":\"\",\"suffixLength\":\"\"}],\"vehicleNetCertNoExample\":\"https://dimg04.c-ctrip.com/images/0412r120008x89sk55505_W_300_0.png\",\"driverNetCertNoExample\":\"\"}}");
//        NetCertCheckRuleDTO netCertCheckRuleDTO2 = driverVehicleNetCertCheckRuleConfig.getRuleByCity("32");
//        Assert.assertTrue(netCertCheckRuleDTO2 != null);
//        Assert.assertTrue(netCertCheckRuleDTO2.getDriverNetCertNoCheckRule() != null);
//    }
//    @Test
//    public void testVehicleNetCertNoExampleNull()throws Exception{
//        Field field = DriverVehicleNetCertCheckRuleConfig.class.getDeclaredField("checkRuleConfig");
//        FieldSetter.setField(driverVehicleNetCertCheckRuleConfig,field,"{\"32\":{}}");
//        NetCertCheckRuleDTO netCertCheckRuleDTO2 = driverVehicleNetCertCheckRuleConfig.getRuleByCity("32");
//        Assert.assertTrue(netCertCheckRuleDTO2 == null);
//    }
//    @Test
//    public void testVehicleNetCertNoCheckRuleNull()throws Exception{
//        Field field = DriverVehicleNetCertCheckRuleConfig.class.getDeclaredField("checkRuleConfig");
//        FieldSetter.setField(driverVehicleNetCertCheckRuleConfig,field,"{\"32\":{\"vehicleNetCertNoCheckRule\":[],\"driverNetCertNoCheckRule\":[{\"totalLength\":\"18\",\"prefix\":\"\",\"suffixType\":\"\",\"suffixLength\":\"\"},{\"totalLength\":\"15\",\"prefix\":\"\",\"suffixType\":\"\",\"suffixLength\":\"\"}],\"vehicleNetCertNoExample\":\"https://dimg04.c-ctrip.com/images/0412r120008x89sk55505_W_300_0.png\",\"driverNetCertNoExample\":\"\"},\"shen_zhen\":{\"vehicleNetCertNoCheckRule\":[{\"totalLength\":\"9\",\"prefix\":\"900\",\"suffixType\":\"NUMBER\",\"suffixLength\":\"6\"}],\"driverNetCertNoCheckRule\":[{\"totalLength\":\"10\",\"prefix\":\"9000\",\"suffixType\":\"NUMBER\",\"suffixLength\":\"6\"}],\"vehicleNetCertNoExample\":\"https://dimg04.c-ctrip.com/images/0412r120008x89sk55505_W_300_0.png\",\"driverNetCertNoExample\":\"https://dimg04.c-ctrip.com/images/0412r120008x89sk55505_W_300_0.png\"},\"san_ya\":{\"vehicleNetCertNoCheckRule\":[{\"totalLength\":\"12\",\"prefix\":\"460200\",\"suffixType\":\"NUMBER\",\"suffixLength\":\"6\"}],\"driverNetCertNoCheckRule\":[{\"totalLength\":\"18\",\"prefix\":\"\",\"suffixType\":\"\",\"suffixLength\":\"\"},{\"totalLength\":\"15\",\"prefix\":\"\",\"suffixType\":\"\",\"suffixLength\":\"\"}],\"vehicleNetCertNoExample\":\"\",\"driverNetCertNoExample\":\"\"}}");
//        NetCertCheckRuleDTO netCertCheckRuleDTO2 = driverVehicleNetCertCheckRuleConfig.getRuleByCity("32");
//        Assert.assertTrue(netCertCheckRuleDTO2 == null);
//    }
//    @Test
//    public void testDriverNetCertNoCheckRuleNull()throws Exception{
//        Field field = DriverVehicleNetCertCheckRuleConfig.class.getDeclaredField("checkRuleConfig");
//        FieldSetter.setField(driverVehicleNetCertCheckRuleConfig,field,"{\"32\":{\"vehicleNetCertNoCheckRule\":[{\"totalLength\":\"7\",\"prefix\":\"0\",\"suffixType\":\"NUMBER\",\"suffixLength\":\"6\"}],\"driverNetCertNoCheckRule\":[],\"vehicleNetCertNoExample\":\"https://dimg04.c-ctrip.com/images/0412r120008x89sk55505_W_300_0.png\",\"driverNetCertNoExample\":\"\"},\"shen_zhen\":{\"vehicleNetCertNoCheckRule\":[{\"totalLength\":\"9\",\"prefix\":\"900\",\"suffixType\":\"NUMBER\",\"suffixLength\":\"6\"}],\"driverNetCertNoCheckRule\":[{\"totalLength\":\"10\",\"prefix\":\"9000\",\"suffixType\":\"NUMBER\",\"suffixLength\":\"6\"}],\"vehicleNetCertNoExample\":\"https://dimg04.c-ctrip.com/images/0412r120008x89sk55505_W_300_0.png\",\"driverNetCertNoExample\":\"https://dimg04.c-ctrip.com/images/0412r120008x89sk55505_W_300_0.png\"},\"san_ya\":{\"vehicleNetCertNoCheckRule\":[{\"totalLength\":\"12\",\"prefix\":\"460200\",\"suffixType\":\"NUMBER\",\"suffixLength\":\"6\"}],\"driverNetCertNoCheckRule\":[{\"totalLength\":\"18\",\"prefix\":\"\",\"suffixType\":\"\",\"suffixLength\":\"\"},{\"totalLength\":\"15\",\"prefix\":\"\",\"suffixType\":\"\",\"suffixLength\":\"\"}],\"vehicleNetCertNoExample\":\"https://dimg04.c-ctrip.com/images/0412r120008x89sk55505_W_300_0.png\",\"driverNetCertNoExample\":\"\"}}");
//        NetCertCheckRuleDTO netCertCheckRuleDTO2 = driverVehicleNetCertCheckRuleConfig.getRuleByCity("32");
//        Assert.assertTrue(netCertCheckRuleDTO2 == null);
//    }
//}
