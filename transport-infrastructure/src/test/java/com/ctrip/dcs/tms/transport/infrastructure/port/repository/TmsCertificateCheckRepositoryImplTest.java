package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.powermock.api.mockito.*;
import org.powermock.core.classloader.annotations.*;
import org.powermock.modules.junit4.*;

import java.util.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DalQueryDao.class, TmsCertificateCheckRepositoryImpl.class, DalTableDao.class})
@PowerMockIgnore({"javax.management.*","javax.crypto.*"})
public class TmsCertificateCheckRepositoryImplTest {


    @Test
    public void queryCertificateByCheckId() throws Exception{

        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        TmsCertificateCheckPO tmsCertificateCheckPO = PowerMockito.mock(TmsCertificateCheckPO.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        StatementParameters parameters =PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        RecruitingSOARequestDTO soaRequestDTO = new RecruitingSOARequestDTO();
        soaRequestDTO.setDatachangeCreatetimeEnd("2020-02-02 12:12:12");
        PaginatorDTO paginator = new PaginatorDTO();
        paginator.setPageNo(1);
        paginator.setPageSize(10);
        List<Long> checkIds = Arrays.asList(1L);
        List<Integer> certificateTypeList = Arrays.asList(1,2,3);
        List<TmsCertificateCheckPO> list =relationRepository.queryCertificateByCheckId(1L,1);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void updateCheckStatus() throws Exception{
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        int i = relationRepository.updateCheckStatus(1L,1);
        Assert.assertTrue(i == 0);
    }

    @Test
    public void queryIdCardCheckByCheckIng() throws Exception{
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        RecruitingSOARequestDTO soaRequestDTO = new RecruitingSOARequestDTO();
        soaRequestDTO.setDatachangeCreatetimeEnd("2020-02-02 12:12:12");
        PaginatorDTO paginator = new PaginatorDTO();
        paginator.setPageNo(1);
        paginator.setPageSize(10);
        List<Long> checkIds = Arrays.asList(1L);
        List<Integer> certificateTypeList = Arrays.asList(1,2,3);
        List<TmsCertificateCheckPO>  list = relationRepository.queryIdCardCheckByCheckIng(checkIds,1,certificateTypeList,1,1,20);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void countIdCardCheckByCheckIng() throws Exception{
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        RecruitingSOARequestDTO soaRequestDTO = new RecruitingSOARequestDTO();
        soaRequestDTO.setDatachangeCreatetimeEnd("2020-02-02 12:12:12");
        PaginatorDTO paginator = new PaginatorDTO();
        paginator.setPageNo(1);
        paginator.setPageSize(10);
        List<Long> checkIds = Arrays.asList(1L);
        List<Integer> certificateTypeList = Arrays.asList(1,2,3);
        int pos = relationRepository.countIdCardCheckByCheckIng(checkIds,1,certificateTypeList,1);
        Assert.assertTrue(pos == 0);
    }

    @Test
    public void synchronousCheck() throws Exception{
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        int i = relationRepository.synchronousCheck(Arrays.asList(1L),1L,1);
        Assert.assertTrue(i == 0);
    }

    @Test
    public void queryCerCheckListByCheckIds() throws Exception{
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        TmsCertificateCheckPO tmsCertificateCheckPO = PowerMockito.mock(TmsCertificateCheckPO.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        StatementParameters parameters =PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        RecruitingSOARequestDTO soaRequestDTO = new RecruitingSOARequestDTO();
        soaRequestDTO.setDatachangeCreatetimeEnd("2020-02-02 12:12:12");
        PaginatorDTO paginator = new PaginatorDTO();
        paginator.setPageNo(1);
        paginator.setPageSize(10);
        List<Long> checkIds = Arrays.asList(1L);
        List<Integer> certificateTypeList = Arrays.asList(1,2,3);
        List<TmsCertificateCheckPO> list = relationRepository.queryCerCheckListByCheckIds(1L,1);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void queryCerCheckListByKeyWord() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        List<TmsCertificateCheckPO>  list = relationRepository.queryCerCheckListByKeyWord("111",1);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void queryCerCheckListByKeyWord1() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        List<TmsCertificateCheckPO> list = relationRepository.queryCerCheckListByKeyWord(Arrays.asList("111"),1,1,1,1,1);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void countCerCheckListByKeyWord() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().count(builder,hints)).thenReturn(1);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        try {
            int i = relationRepository.countCerCheckListByKeyWord(Arrays.asList("111"),1,1,1);
            Assert.assertTrue(i == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void queryCerCheckListByCheckIds1() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        try {
            List<TmsCertificateCheckPO>  list = relationRepository.queryCerCheckListByCheckIds(Arrays.asList(1L),Arrays.asList(1));
            Assert.assertTrue(list.isEmpty());
        }catch (Exception e){

        }
    }

    @Test
    public void queryCerCheckListByIds() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        try {
            List<TmsCertificateCheckPO>  list = relationRepository.queryCerCheckListByIds(Arrays.asList(1L));
            Assert.assertTrue(list.isEmpty());
        }catch (Exception e){

        }
    }

    @Test
    public void batchUpdateCheckStatus() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeUpdateSqlBuilder builder = PowerMockito.mock(FreeUpdateSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getQueryDao().update(builder,hints)).thenReturn(1);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        try {
            int i = relationRepository.batchUpdateCheckStatus(Arrays.asList(353935L),3);
            Assert.assertTrue(i == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void queryCerCheckListByKeyWordOrderById() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        try {
            List<TmsCertificateCheckPO>  list = relationRepository.queryCerCheckListByKeyWordOrderById(1L,"11",1,2,3,1,1);
            Assert.assertTrue(list.isEmpty());
        }catch (Exception e){

        }
    }

    @Test
    public void queryCertificateByCheckIdOrderBy() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        try {
            List<TmsCertificateCheckPO>  list = relationRepository.queryCertificateByCheckIdOrderBy(1L,1);
            Assert.assertTrue(list.isEmpty());
        }catch (Exception e){

        }
    }

    @Test
    public void updateCheckStatus1() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeUpdateSqlBuilder builder = PowerMockito.mock(FreeUpdateSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getQueryDao().update(builder,hints)).thenReturn(1);
        try {
            int i = relationRepository.updateCheckStatus(1L,1,"11");
            Assert.assertTrue(i == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void updateIdcardCheckStatus() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeUpdateSqlBuilder builder = PowerMockito.mock(FreeUpdateSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getQueryDao().update(builder,hints)).thenReturn(1);
        try {
            int i = relationRepository.updateIdcardCheckStatus(1L,1,true,"11");
            Assert.assertTrue(i == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void queryNewCheckListByCheckIds() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        try {
            List<TmsCertificateCheckPO>  list  = relationRepository.queryNewCheckListByCheckIds(Arrays.asList(1L),1,true,1);
            Assert.assertTrue(list.isEmpty());
        }catch (Exception e){

        }
    }

    @Test
    public void updateCertificateActive() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        int i = relationRepository.updateCertificateActive(0L,1,1,true,"system");
        Assert.assertTrue(i == 0);
    }

    @Test
    public void updateCertificateStatus() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeUpdateSqlBuilder builder = PowerMockito.mock(FreeUpdateSqlBuilder.class);
        StatementParameters parameters =PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getQueryDao().update(builder,parameters,hints)).thenReturn(1);
        int i = relationRepository.updateCertificateStatus(Arrays.asList(1L),1,1,1,"system");
        Assert.assertTrue(i == 0);
    }

    @Test
    public void updateCertificateActive1() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsCertificateCheckRepositoryImpl relationRepository = new TmsCertificateCheckRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeUpdateSqlBuilder builder = PowerMockito.mock(FreeUpdateSqlBuilder.class);
        StatementParameters parameters =PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getQueryDao().update(builder,parameters,hints)).thenReturn(1);
        int i = relationRepository.updateCertificateActive(1L,1,Arrays.asList(11),false,"system");
        Assert.assertTrue(i == 0);
    }
}
