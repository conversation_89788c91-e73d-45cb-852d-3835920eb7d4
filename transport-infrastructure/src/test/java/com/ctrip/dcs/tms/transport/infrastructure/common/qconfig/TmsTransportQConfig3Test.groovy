package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig


import spock.lang.Specification
import spock.lang.Unroll

class TmsTransportQConfig3Test extends Specification {
    def testObj = new TmsTransportQconfig()

    def setup() {

    }

    @Unroll
    def "getQueryLeavelPoolCoreSizeTest"() {

        when:
        def result = testObj.getQueryLeavelPoolCoreSize()

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult << 4
    }


    @Unroll
    def "getQueryLeavelPoolMaxSizeTest"() {

        when:
        def result = testObj.getQueryLeavelPoolMaxSize()

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult << 4
    }


    @Unroll
    def "getQueryLeavelPoolWorkQueueSizeTest"() {

        when:
        def result = testObj.getQueryLeavelPoolWorkQueueSize()

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult << 2000
    }


    @Unroll
    def "getQueryTransportPoolCoreSizeTest"() {

        when:
        def result = testObj.getQueryTransportPoolCoreSize()

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult << 4
    }


    @Unroll
    def "getQueryTransportPoolMaxSizeTest"() {

        when:
        def result = testObj.getQueryTransportPoolMaxSize()

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult << 4
    }


    @Unroll
    def "getQueryTransportPoolWorkQueueSizeTest"() {

        when:
        def result = testObj.getQueryTransportPoolWorkQueueSize()

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult << 2000
    }


    @Unroll
    def "getQueryDriverPoolCoreSizeTest"() {

        when:
        def result = testObj.getQueryDriverPoolCoreSize()

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult << 4
    }


    @Unroll
    def "getQueryDriverPoolMaxSizeTest"() {

        when:
        def result = testObj.getQueryDriverPoolMaxSize()

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult << 4
    }


    @Unroll
    def "getQueryDriverPoolWorkQueueSizeTest"() {

        when:
        def result = testObj.getQueryDriverPoolWorkQueueSize()

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult << 2000
    }


    @Unroll
    def "getInQueryBatchSizeTest"() {

        when:
        def result = testObj.getInQueryBatchSize()

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult << 1000
    }


    @Unroll
    def "getCheckMobileSwitchTest"() {

        when:
        def result = testObj.getCheckMobileSwitch()

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult << Boolean.TRUE
    }


}

