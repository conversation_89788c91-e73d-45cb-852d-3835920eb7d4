package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.google.common.collect.*;
import org.junit.Assert;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.powermock.api.mockito.*;
import org.powermock.core.classloader.annotations.*;
import org.powermock.modules.junit4.*;
import org.springframework.util.*;

import java.util.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DalQueryDao.class, DrvRecruitingRepositoryImpl.class,DalTableDao.class})
@PowerMockIgnore({"javax.management.*","javax.crypto.*"})
public class DrvRecruitingRepositoryImplTest {
    @Test
    public void updateCheckStatus() throws Exception{
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dalTableDao = PowerMockito.mock(DalQueryDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeUpdateSqlBuilder builder = PowerMockito.mock(FreeUpdateSqlBuilder.class);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao().update(builder,hints)).thenReturn(1);
        List<Long> drvRecruitingIds = Lists.newArrayList();
        drvRecruitingIds.add(11L);
        int pos = relationRepository.updateCheckStatus(drvRecruitingIds,1);
        Assert.assertTrue(pos == 0);
    }

    @Test
    public void queryDrvRecruitingBySupplierIdAndId() throws Exception{
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        List<DrvRecruitingPO> pos = relationRepository.queryDrvRecruitingBySupplierIdAndId(Arrays.asList(1L),1L);
        Assert.assertTrue(pos.size() == 0);
    }

    @Test
    public void unBingVehicleFromDrvRecruiting() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dalTableDao = PowerMockito.mock(DalQueryDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeUpdateSqlBuilder builder = PowerMockito.mock(FreeUpdateSqlBuilder.class);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao().update(builder,hints)).thenReturn(1);
        List<Long> drvRecruitingIds = Lists.newArrayList();
        drvRecruitingIds.add(1L);
        int pos = relationRepository.unBingVehicleFromDrvRecruiting(drvRecruitingIds,"system");
        Assert.assertTrue(pos == 0);
    }

    @Test
    public void countApproveIngDrvRecruiting() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().count(builder,hints)).thenReturn(1);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        try {
            int pos = relationRepository.countApproveIngDrvRecruiting(Arrays.asList(1L),1,1);
            Assert.assertTrue(pos == 0);
        }catch (Exception e){

        }
    }
    @Test
    public void queryApproveIngDrvRecruiting() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        try {
            List<DrvRecruitingPO> pos = relationRepository.queryApproveIngDrvRecruiting(Arrays.asList(1L),1,1);
            Assert.assertTrue(pos.size() == 0);
        }catch (Exception e){

        }
    }


    @Test
    public void updateDrvRecApproverStatus() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dalTableDao = PowerMockito.mock(DalQueryDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeUpdateSqlBuilder builder = PowerMockito.mock(FreeUpdateSqlBuilder.class);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao().update(builder,hints)).thenReturn(1);
        List<Long> drvRecruitingIds = Lists.newArrayList();
        drvRecruitingIds.add(1L);
        int pos = relationRepository.updateDrvRecApproverStatus(drvRecruitingIds,1,"1","1",1,true,0);
        Assert.assertTrue(pos == 0);
    }

    @Test
    public void queryvRecruitingByVehicleId() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dalTableDao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao1 = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dalTableDao);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao1);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        List<DrvRecruitingPO> list = relationRepository.queryvRecruitingByVehicleId(1L);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void updateDrvCheckStatus() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        DalQueryDao dalTableDao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao1 = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dalTableDao);
        FreeUpdateSqlBuilder builder = PowerMockito.mock(FreeUpdateSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao1);
        PowerMockito.when(dalRepository.getQueryDao().update(builder,parameters,hints)).thenReturn(1);
        int i = relationRepository.updateDrvCheckStatus(1L,1,"111");
        Assert.assertTrue(i == 0);
    }

    @Test
    public void checkRecruitingDrvOnly() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dalTableDao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao1 = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dalTableDao);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao1);
        PowerMockito.when(dalRepository.getDao().count(builder,hints)).thenReturn(1);
        try {
            Boolean b = relationRepository.checkRecruitingDrvOnly("1",1);
            Assert.assertTrue(b);
        }catch (Exception e){

        }
    }

    @Test
    public void countWaitApproveRecruitingDrv() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dalTableDao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao1 = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dalTableDao);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao1);
        PowerMockito.when(dalRepository.getDao().count(builder,hints)).thenReturn(1);
        try {
            int i = relationRepository.countWaitApproveRecruitingDrv();
            Assert.assertTrue(i == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void queryWaitApproveRecruitingDrvByPage() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dalTableDao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao1 = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dalTableDao);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao1);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        try {
            List<DrvRecruitingPO> list = relationRepository.queryWaitApproveRecruitingDrvByPage(1,10);
            Assert.assertTrue(list.isEmpty());
        }catch (Exception e){

        }
    }

    @Test
    public void queryRecruitingDrvByPhone() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dalTableDao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao1 = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dalTableDao);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao1);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        try {
            List<DrvRecruitingPO> list = relationRepository.queryRecruitingDrvByPhone("1",1L);
            Assert.assertTrue(list.isEmpty());
        }catch (Exception e){

        }
    }

    @Test
    public void queryDomesticRecruitingDrvByPhone() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dalTableDao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao1 = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dalTableDao);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao1);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        try {
            List<DrvRecruitingPO> list = relationRepository.queryDomesticRecruitingDrvByPhone("1");
            Assert.assertTrue(list.isEmpty());
        }catch (Exception e){

        }
    }

    @Test
    public void updateDrvApproveSchedule() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dalTableDao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao1 = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dalTableDao);
        FreeUpdateSqlBuilder builder = PowerMockito.mock(FreeUpdateSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao1);
        PowerMockito.when(dalRepository.getQueryDao().update(builder,hints)).thenReturn(1);
        try {
            int i= relationRepository.updateDrvApproveSchedule(1L,1,1);
            Assert.assertTrue(i == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void updateDrvApproveSchedule1() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dalTableDao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao1 = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dalTableDao);
        FreeUpdateSqlBuilder builder = PowerMockito.mock(FreeUpdateSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao1);
        PowerMockito.when(dalRepository.getQueryDao().update(builder,hints)).thenReturn(1);
        try {
            int i = relationRepository.updateDrvApproveSchedule(1L,2,1);
            Assert.assertTrue(i == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void updateApproveAging() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dalTableDao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao1 = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dalTableDao);
        FreeUpdateSqlBuilder builder = PowerMockito.mock(FreeUpdateSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao1);
        PowerMockito.when(dalRepository.getQueryDao().update(builder,hints)).thenReturn(1);
        try {
            int i = relationRepository.updateApproveAging(1L);
            Assert.assertTrue(i == 0);
        }catch (Exception e){

        }
    }


    @Test
    public void findNewDrvRecruitingCount() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getDao().count(builder,hints)).thenReturn(1);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        RecruitingSOARequestDTO recruitingSOARequestDTO = new RecruitingSOARequestDTO();
        recruitingSOARequestDTO.setRecruitingId(1L);
        recruitingSOARequestDTO.setRecruitingIdList(Arrays.asList(1L));
        recruitingSOARequestDTO.setSupplierId(1L);
        recruitingSOARequestDTO.setApproveStatus(1);
        recruitingSOARequestDTO.setVehicleTypeId(1L);
        recruitingSOARequestDTO.setCityId(1L);
        recruitingSOARequestDTO.setVehicleLicense("京A");
        recruitingSOARequestDTO.setDrvName("11");
        recruitingSOARequestDTO.setDrvEnglishName("en");
        recruitingSOARequestDTO.setDrvPhone("11");
        recruitingSOARequestDTO.setDrvIdcard("111");
        recruitingSOARequestDTO.setDrvLanguage("11");
        recruitingSOARequestDTO.setDatachangeCreatetimeStart("2022-01-01 12:12:12");
        recruitingSOARequestDTO.setDatachangeCreatetimeEnd("2022-07-01 12:12:12");
        recruitingSOARequestDTO.setCheckStatus(1);
        recruitingSOARequestDTO.setApproveSchedule(1);
        recruitingSOARequestDTO.setApproveAging(1);
        try {
            Long i = relationRepository.findNewDrvRecruitingCount(recruitingSOARequestDTO,Arrays.asList(4),Arrays.asList(4),1);
            Assert.assertTrue(i!=null);
        }catch (Exception e){

        }
    }
    @Test
    public void testqueryDriverRecruitingInfo()throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalTableDao.query(Mockito.any(SelectSqlBuilder.class),Mockito.any(DalHints.class))).thenReturn(Arrays.asList(new DrvRecruitingPO()));

        String driverPhone = "13011132450";
        String driverIdCard = "**********";
        String vehicleLicense = "京A21345";
        List<Long> drvRecruitingIds = Arrays.asList(1L,2L);
        List<DrvRecruitingPO> drvRecruitingPOS = relationRepository.queryDriverRecruitingInfo(driverPhone,driverIdCard,vehicleLicense,drvRecruitingIds);
        Assert.assertTrue(!CollectionUtils.isEmpty(drvRecruitingPOS));
    }

    @Test
    public void queryvRecruitingByVehicleIds() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dalTableDao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao1 = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dalTableDao);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao1);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        try {
            List<DrvRecruitingPO> i= relationRepository.queryvRecruitingByVehicleIds(Arrays.asList(1L));
            Assert.assertTrue(i.isEmpty());
        }catch (Exception e){

        }
    }

    @Test
    public void queryRecruitingDrvByPhoneORId() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dalTableDao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao1 = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dalTableDao);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao1);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        try {
            List<DrvRecruitingPO> i= relationRepository.queryRecruitingDrvByPhoneORId("111",1L);
            Assert.assertTrue(i.isEmpty());
        }catch (Exception e){

        }
    }

    @Test
    public void findDrvRecruitingIdList() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeSelectSqlBuilder builder = PowerMockito.mock(FreeSelectSqlBuilder.class);
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getQueryDao().query(builder,parameters,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        PaginatorDTO paginatorDTO = new PaginatorDTO();
        paginatorDTO.setPageNo(1);
        paginatorDTO.setPageSize(10);
        RecruitingSOARequestDTO recruitingSOARequestDTO = new RecruitingSOARequestDTO();
        recruitingSOARequestDTO.setRecruitingId(1L);
        recruitingSOARequestDTO.setRecruitingIdList(Arrays.asList(1L));
        recruitingSOARequestDTO.setSupplierId(1L);
        recruitingSOARequestDTO.setApproveStatus(1);
        recruitingSOARequestDTO.setVehicleTypeId(1L);
        recruitingSOARequestDTO.setCityId(1L);
        recruitingSOARequestDTO.setVehicleLicense("京A");
        recruitingSOARequestDTO.setDrvName("11");
        recruitingSOARequestDTO.setDrvEnglishName("en");
        recruitingSOARequestDTO.setDrvPhone("11");
        recruitingSOARequestDTO.setDrvIdcard("111");
        recruitingSOARequestDTO.setDrvLanguage("11");
        recruitingSOARequestDTO.setDatachangeCreatetimeStart("2022-01-01 12:12:12");
        recruitingSOARequestDTO.setDatachangeCreatetimeEnd("2022-07-01 12:12:12");
        recruitingSOARequestDTO.setCheckStatus(1);
        recruitingSOARequestDTO.setApproveSchedule(1);
        recruitingSOARequestDTO.setApproveAging(1);
        List<Long> list = relationRepository.findDrvRecruitingIdList(recruitingSOARequestDTO,paginatorDTO,Arrays.asList(4),1);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void queryDrvRecruitingListByIds() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeSelectSqlBuilder builder = PowerMockito.mock(FreeSelectSqlBuilder.class);
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getQueryDao().query(builder,parameters,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        List<DrvRecruitingPO> list = relationRepository.queryDrvRecruitingListByIds(Arrays.asList(1L));
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void findDrvRecruitingIdList1() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeSelectSqlBuilder builder = PowerMockito.mock(FreeSelectSqlBuilder.class);
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getQueryDao().query(builder,parameters,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        PaginatorDTO paginatorDTO = new PaginatorDTO();
        paginatorDTO.setPageNo(1);
        paginatorDTO.setPageSize(10);
        RecruitingSOARequestDTO recruitingSOARequestDTO = new RecruitingSOARequestDTO();
        List<Long> list = relationRepository.findDrvRecruitingIdList(recruitingSOARequestDTO,paginatorDTO,Arrays.asList(4),1);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void testCountDrvDirtyPhone() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().count(builder,hints)).thenReturn(1);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        try {
            int pos = relationRepository.countDrvDirtyPhone();
            Assert.assertTrue(pos == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void testQueryDrvDirtyPhone() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl drvRecruitingRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        List<DrvRecruitingPO> list = drvRecruitingRepository.queryDrvDirtyPhone(1, 100);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void testCountRecruitingPhone() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvRecruitingRepositoryImpl relationRepository = new DrvRecruitingRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().count(builder,hints)).thenReturn(1);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        try {
            DrvRecruitingPO pos = relationRepository.queryOneByRecruitingPhone("111");
            Assert.assertNull(pos);
        } catch (Exception e){

        }
    }
}
