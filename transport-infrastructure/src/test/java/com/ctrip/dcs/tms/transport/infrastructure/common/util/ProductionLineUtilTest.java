package com.ctrip.dcs.tms.transport.infrastructure.common.util;

import com.ctrip.dcs.scm.sdk.common.CategoryEnum;
import com.ctrip.dcs.scm.sdk.domain.CategoryRepository;
import com.ctrip.dcs.scm.sdk.domain.category.Category;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupDriverRelationPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CommonEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.BindRuleConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ProductionCategoryLineConfig;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupDriverRelationRepository;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 产线工具配套测试用例
 *
 * <AUTHOR>
 * 2021-05-19 17:57
 */
@RunWith(MockitoJUnitRunner.class)
public class ProductionLineUtilTest {

    @InjectMocks
    private ProductionLineUtil productionLineUtil;

    @Mock
    private CategoryRepository categoryRepository;

    @Mock
    private ProductionCategoryLineConfig productionCategoryLineConfig;

    @Mock
    BindRuleConfig bindRuleConfig;

    @Mock
    TspTransportGroupDriverRelationRepository tspTransportGroupDriverRelationRepository;
    @Mock
    TransportGroupRepository transportGroupRepository;

    /**
     * 同步于 production.category.line.json
     */
    private final Integer JNT_SHOW_CODE = 1;
    private final Integer RTN_SHOW_CODE = 2;
    private final Integer DAY_SHOW_CODE = 3;
    private final Integer PTP_SHOW_CODE = 22;

    @Before
    public void ready() {

        Mockito.when(productionCategoryLineConfig.getShowToUseLineCodeMap()).thenReturn(ImmutableMap.of(JNT_SHOW_CODE, CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue(), RTN_SHOW_CODE, CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue(), DAY_SHOW_CODE, CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue(), PTP_SHOW_CODE, CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue()));
        Mockito.when(productionCategoryLineConfig.getUseToShowLineCodeMap()).thenReturn(ImmutableMap.of(CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue(), JNT_SHOW_CODE, CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue(), RTN_SHOW_CODE, CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue(), DAY_SHOW_CODE, CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue(), PTP_SHOW_CODE));
        Mockito.when(productionCategoryLineConfig.getUseToLineCodeMap()).thenReturn(ImmutableMap.of(JNT_SHOW_CODE, CommonEnum.ProductionLineCodeEnum.JNT.getValue(), RTN_SHOW_CODE, CommonEnum.ProductionLineCodeEnum.RTN.getValue(), DAY_SHOW_CODE, CommonEnum.ProductionLineCodeEnum.DAY.getValue(), PTP_SHOW_CODE, CommonEnum.ProductionLineCodeEnum.PTP.getValue()));

        Category categoryJnt = Category.newBuilder().withName(CommonEnum.ProductionLineCodeEnum.JNT.getValue()).build();
        Category categoryDay = Category.newBuilder().withName(CommonEnum.ProductionLineCodeEnum.DAY.getValue()).build();
        Category categoryRtn = Category.newBuilder().withName(CommonEnum.ProductionLineCodeEnum.RTN.getValue()).build();
        Category categoryPtp = Category.newBuilder().withName(CommonEnum.ProductionLineCodeEnum.PTP.getValue()).build();

        Mockito.when(categoryRepository.findOne(JNT_SHOW_CODE.longValue())).thenReturn(categoryJnt);
        Mockito.when(categoryRepository.findOne(RTN_SHOW_CODE.longValue())).thenReturn(categoryRtn);
        Mockito.when(categoryRepository.findOne(DAY_SHOW_CODE.longValue())).thenReturn(categoryDay);
        Mockito.when(categoryRepository.findOne(PTP_SHOW_CODE.longValue())).thenReturn(categoryPtp);


        Mockito.when(productionCategoryLineConfig.getShowProductCode(CommonEnum.ProductionLineCodeEnum.JNT)).thenReturn(JNT_SHOW_CODE);
        Mockito.when(productionCategoryLineConfig.getShowProductCode(CommonEnum.ProductionLineCodeEnum.DAY)).thenReturn(DAY_SHOW_CODE);
//        Mockito.when(productionCategoryLineConfig.getShowProductCode(CommonEnum.ProductionLineCodeEnum.RTN)).thenReturn(RTN_SHOW_CODE);
        Mockito.when(productionCategoryLineConfig.getShowProductCode(CommonEnum.ProductionLineCodeEnum.PTP)).thenReturn(PTP_SHOW_CODE);


        Mockito.when(productionCategoryLineConfig.getProductCode(CommonEnum.ProductionLineCodeEnum.JNT)).thenReturn(CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue());
        Mockito.when(productionCategoryLineConfig.getProductCode(CommonEnum.ProductionLineCodeEnum.DAY)).thenReturn(CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue());
        Mockito.when(productionCategoryLineConfig.getProductCode(CommonEnum.ProductionLineCodeEnum.RTN)).thenReturn(CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue());
        Mockito.when(productionCategoryLineConfig.getProductCode(CommonEnum.ProductionLineCodeEnum.PTP)).thenReturn(CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue());
    }

    @Test
    public void AndTest() {

        Assert.assertTrue((CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() & CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue()) == CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() & CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue()) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() & CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue()) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() & CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue()) == 0);

        Assert.assertTrue((CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() & CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue()) == CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() & CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue()) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() & CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue()) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() & CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue()) == 0);

        Assert.assertTrue((CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() & CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue()) == CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() & CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue()) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() & CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue()) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() & CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue()) == 0);

        Assert.assertTrue((CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() & CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue()) == CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() & CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue()) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() & CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue()) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() & CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue()) == 0);

        Assert.assertTrue((CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue());

        Assert.assertTrue((CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue());

        Assert.assertTrue((CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue())) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue());

        Assert.assertTrue((CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue())) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == 0);
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue());
        Assert.assertTrue((CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue() & (CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue())) == CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue());
    }

    @Test
    public void getIntegratedLineTest() {

        Assert.assertTrue(checkListEquals(Lists.newArrayList(), productionLineUtil.getIncludeProductionLineList(Lists.newArrayList())));

        Assert.assertTrue(checkListEquals(Lists.newArrayList(1, 3, 5, 7, 9, 11, 13, 15), productionLineUtil.getIncludeProductionLineList(ImmutableList.of(JNT_SHOW_CODE))));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(2, 3, 6, 7, 10, 11, 14, 15), productionLineUtil.getIncludeProductionLineList(ImmutableList.of(RTN_SHOW_CODE))));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(4, 5, 6, 7, 12, 13, 14, 15), productionLineUtil.getIncludeProductionLineList(ImmutableList.of(DAY_SHOW_CODE))));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(8, 9, 10, 11, 12, 13, 14, 15), productionLineUtil.getIncludeProductionLineList(ImmutableList.of(PTP_SHOW_CODE))));

        Assert.assertTrue(checkListEquals(Lists.newArrayList(1, 2, 3, 5, 6, 7, 9, 10, 11, 13, 14, 15), productionLineUtil.getIncludeProductionLineList(ImmutableList.of(JNT_SHOW_CODE, RTN_SHOW_CODE))));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(1, 3, 4, 5, 6, 7, 9, 11, 12, 13, 14, 15), productionLineUtil.getIncludeProductionLineList(ImmutableList.of(JNT_SHOW_CODE, DAY_SHOW_CODE))));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(2, 3, 4, 5, 6, 7, 10, 11, 12, 13, 14, 15), productionLineUtil.getIncludeProductionLineList(ImmutableList.of(RTN_SHOW_CODE, DAY_SHOW_CODE))));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(1, 3, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15), productionLineUtil.getIncludeProductionLineList(ImmutableList.of(JNT_SHOW_CODE, PTP_SHOW_CODE))));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(2, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15), productionLineUtil.getIncludeProductionLineList(ImmutableList.of(RTN_SHOW_CODE, PTP_SHOW_CODE))));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15), productionLineUtil.getIncludeProductionLineList(ImmutableList.of(DAY_SHOW_CODE, PTP_SHOW_CODE))));

        Assert.assertTrue(checkListEquals(Lists.newArrayList(1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 15), productionLineUtil.getIncludeProductionLineList(ImmutableList.of(JNT_SHOW_CODE, RTN_SHOW_CODE, DAY_SHOW_CODE))));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15), productionLineUtil.getIncludeProductionLineList(ImmutableList.of(JNT_SHOW_CODE, RTN_SHOW_CODE, DAY_SHOW_CODE, PTP_SHOW_CODE))));
    }

    @Test
    public void getShowProductionLineListTest() {
        Assert.assertTrue(1 == productionLineUtil.getIntegratedLine(Lists.newArrayList()));
        Assert.assertTrue(1 == productionLineUtil.getIntegratedLine(Lists.newArrayList(JNT_SHOW_CODE)));
        Assert.assertTrue(2 == productionLineUtil.getIntegratedLine(Lists.newArrayList(RTN_SHOW_CODE)));
        Assert.assertTrue(4 == productionLineUtil.getIntegratedLine(Lists.newArrayList(DAY_SHOW_CODE)));
        Assert.assertTrue(3 == productionLineUtil.getIntegratedLine(Lists.newArrayList(JNT_SHOW_CODE, RTN_SHOW_CODE)));
        Assert.assertTrue(5 == productionLineUtil.getIntegratedLine(Lists.newArrayList(JNT_SHOW_CODE, DAY_SHOW_CODE)));
        Assert.assertTrue(6 == productionLineUtil.getIntegratedLine(Lists.newArrayList(RTN_SHOW_CODE, DAY_SHOW_CODE)));
        Assert.assertTrue(7 == productionLineUtil.getIntegratedLine(Lists.newArrayList(JNT_SHOW_CODE, RTN_SHOW_CODE, DAY_SHOW_CODE)));
        Assert.assertTrue(8 == productionLineUtil.getIntegratedLine(Lists.newArrayList(PTP_SHOW_CODE)));
        Assert.assertTrue(9 == productionLineUtil.getIntegratedLine(Lists.newArrayList(JNT_SHOW_CODE, PTP_SHOW_CODE)));
        Assert.assertTrue(10 == productionLineUtil.getIntegratedLine(Lists.newArrayList(RTN_SHOW_CODE, PTP_SHOW_CODE)));
        Assert.assertTrue(11 == productionLineUtil.getIntegratedLine(Lists.newArrayList(JNT_SHOW_CODE, RTN_SHOW_CODE, PTP_SHOW_CODE)));
        Assert.assertTrue(12 == productionLineUtil.getIntegratedLine(Lists.newArrayList(DAY_SHOW_CODE, PTP_SHOW_CODE)));
        Assert.assertTrue(13 == productionLineUtil.getIntegratedLine(Lists.newArrayList(JNT_SHOW_CODE, DAY_SHOW_CODE, PTP_SHOW_CODE)));
        Assert.assertTrue(14 == productionLineUtil.getIntegratedLine(Lists.newArrayList(RTN_SHOW_CODE, DAY_SHOW_CODE, PTP_SHOW_CODE)));
        Assert.assertTrue(15 == productionLineUtil.getIntegratedLine(Lists.newArrayList(JNT_SHOW_CODE, RTN_SHOW_CODE, DAY_SHOW_CODE, PTP_SHOW_CODE)));
    }

    @Test
    public void getIncludeProductionLineListTest() {
        Assert.assertTrue(checkListEquals(Lists.newArrayList(), productionLineUtil.getShowProductionLineList(0)));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(JNT_SHOW_CODE), productionLineUtil.getShowProductionLineList(1)));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(RTN_SHOW_CODE), productionLineUtil.getShowProductionLineList(2)));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(JNT_SHOW_CODE, RTN_SHOW_CODE), productionLineUtil.getShowProductionLineList(3)));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(DAY_SHOW_CODE), productionLineUtil.getShowProductionLineList(4)));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(JNT_SHOW_CODE, DAY_SHOW_CODE), productionLineUtil.getShowProductionLineList(5)));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(RTN_SHOW_CODE, DAY_SHOW_CODE), productionLineUtil.getShowProductionLineList(6)));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(JNT_SHOW_CODE, RTN_SHOW_CODE, DAY_SHOW_CODE), productionLineUtil.getShowProductionLineList(7)));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(PTP_SHOW_CODE), productionLineUtil.getShowProductionLineList(8)));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(JNT_SHOW_CODE, PTP_SHOW_CODE), productionLineUtil.getShowProductionLineList(9)));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(RTN_SHOW_CODE, PTP_SHOW_CODE), productionLineUtil.getShowProductionLineList(10)));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(JNT_SHOW_CODE, RTN_SHOW_CODE, PTP_SHOW_CODE), productionLineUtil.getShowProductionLineList(11)));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(DAY_SHOW_CODE, PTP_SHOW_CODE), productionLineUtil.getShowProductionLineList(12)));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(JNT_SHOW_CODE, DAY_SHOW_CODE, PTP_SHOW_CODE), productionLineUtil.getShowProductionLineList(13)));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(RTN_SHOW_CODE, DAY_SHOW_CODE, PTP_SHOW_CODE), productionLineUtil.getShowProductionLineList(14)));
        Assert.assertTrue(checkListEquals(Lists.newArrayList(JNT_SHOW_CODE, RTN_SHOW_CODE, DAY_SHOW_CODE, PTP_SHOW_CODE), productionLineUtil.getShowProductionLineList(15)));
    }

    @Test
    public void getProductionLineNamesTest() {
        Assert.assertTrue("".equals(productionLineUtil.getProductionLineNames(null)));
        Assert.assertTrue("jnt".equals(productionLineUtil.getProductionLineNames(1)));
        Assert.assertTrue("day".equals(productionLineUtil.getProductionLineNames(4)));
        Assert.assertTrue("jnt,rtn".equals(productionLineUtil.getProductionLineNames(3)));
        Assert.assertTrue("rtn".equals(productionLineUtil.getProductionLineNames(2)));
        Assert.assertTrue("jnt,day".equals(productionLineUtil.getProductionLineNames(5)));
        Assert.assertTrue("rtn,day".equals(productionLineUtil.getProductionLineNames(6)));
        Assert.assertTrue("jnt,rtn,day".equals(productionLineUtil.getProductionLineNames(7)));
        Assert.assertTrue("point".equals(productionLineUtil.getProductionLineNames(8)));
        Assert.assertTrue("jnt,point".equals(productionLineUtil.getProductionLineNames(9)));
        Assert.assertTrue("rtn,point".equals(productionLineUtil.getProductionLineNames(10)));
        Assert.assertTrue("jnt,rtn,point".equals(productionLineUtil.getProductionLineNames(11)));
        Assert.assertTrue("day,point".equals(productionLineUtil.getProductionLineNames(12)));
        Assert.assertTrue("jnt,day,point".equals(productionLineUtil.getProductionLineNames(13)));
        Assert.assertTrue("rtn,day,point".equals(productionLineUtil.getProductionLineNames(14)));
        Assert.assertTrue("jnt,rtn,day,point".equals(productionLineUtil.getProductionLineNames(15)));
    }

    @Test
    public void getSubCategoryCodeTest() {
        Assert.assertTrue(checkStringListEquals(
                Lists.newArrayList(), productionLineUtil.getSubCategoryCode(0L)));
        Assert.assertTrue(checkStringListEquals(
                Lists.newArrayList(CategoryEnum.AIRPORT_PICKUP.getCode(), CategoryEnum.AIRPORT_DROPOFF.getCode(), CategoryEnum.STATION_PICKUP.getCode(), CategoryEnum.STATION_DROPOFF.getCode()), productionLineUtil.getSubCategoryCode(JNT_SHOW_CODE.longValue())));
        Assert.assertTrue(checkStringListEquals(
                Lists.newArrayList(CategoryEnum.TAXI_ONDEMAND.getCode(), CategoryEnum.TAXI_PREBOOK.getCode()), productionLineUtil.getSubCategoryCode(RTN_SHOW_CODE.longValue())));
        Assert.assertTrue(checkStringListEquals(
                Lists.newArrayList(CategoryEnum.DAY_RENTAL.getCode(), CategoryEnum.CUSTOMIZE_DAY_RENTAL.getCode()), productionLineUtil.getSubCategoryCode(DAY_SHOW_CODE.longValue())));
        Assert.assertTrue(checkStringListEquals(
                Lists.newArrayList(CategoryEnum.POINT_TO_POINT.getCode()), productionLineUtil.getSubCategoryCode(PTP_SHOW_CODE.longValue())));
    }


    @Test
    public void getJNTProductLineCodeTest() {
        Assert.assertTrue(productionLineUtil.getJNTProductLineCode() == JNT_SHOW_CODE);
    }

    @Test
    public void getDAYProductLineCodeTest() {
        Assert.assertTrue(productionLineUtil.getDAYProductLineCode() == DAY_SHOW_CODE);
    }


    @Test
    public void getPTPProductLineCodeTest() {
        Assert.assertTrue(productionLineUtil.getPTPProductLineCode() == PTP_SHOW_CODE);
    }

    @Test
    public void getJNTAndDAYProductLineCodeTest() {
        Assert.assertTrue(checkListEquals(productionLineUtil.getJNTAndDAYProductLineCode(), Lists.newArrayList(productionLineUtil.getJNTProductLineCode(), productionLineUtil.getDAYProductLineCode())));
    }


    @Test
    public void bindTransportCheckTest() {

        Assert.assertTrue(productionLineUtil.bindTransportCheck(CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue(), CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue()).isSuccess());
        Assert.assertFalse(productionLineUtil.bindTransportCheck(CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue(), CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue()).isSuccess());
        Assert.assertTrue(productionLineUtil.bindTransportCheck(CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue(), CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue()).isSuccess());

        Assert.assertFalse(productionLineUtil.bindTransportCheck(CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue(), CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue()).isSuccess());
        Assert.assertTrue(productionLineUtil.bindTransportCheck(CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue(), CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue()).isSuccess());
        Assert.assertFalse(productionLineUtil.bindTransportCheck(CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue(), CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue()).isSuccess());

        Assert.assertTrue(productionLineUtil.bindTransportCheck(CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue(), CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue()).isSuccess());
        Assert.assertFalse(productionLineUtil.bindTransportCheck(CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue(), CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue()).isSuccess());
        Assert.assertTrue(productionLineUtil.bindTransportCheck(CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue(), CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue() + CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue()).isSuccess());

        Assert.assertFalse(productionLineUtil.bindTransportCheck(null, CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue()).isSuccess());
        Assert.assertFalse(productionLineUtil.bindTransportCheck(CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue(), null).isSuccess());
        Assert.assertFalse(productionLineUtil.bindTransportCheck(null, null).isSuccess());

        Assert.assertFalse(productionLineUtil.bindTransportCheck(CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue(), CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue()).isSuccess());
    }

    @Test
    public void isProductLineCodeCheckTest() {
        Assert.assertTrue(productionLineUtil.isProductLineCodeCheck(Lists.newArrayList(JNT_SHOW_CODE), JNT_SHOW_CODE));
        Assert.assertFalse(productionLineUtil.isProductLineCodeCheck(Lists.newArrayList(), JNT_SHOW_CODE));
        Assert.assertFalse(productionLineUtil.isProductLineCodeCheck(Lists.newArrayList(JNT_SHOW_CODE, DAY_SHOW_CODE), JNT_SHOW_CODE));
        Assert.assertFalse(productionLineUtil.isProductLineCodeCheck(Lists.newArrayList(JNT_SHOW_CODE), DAY_SHOW_CODE));
    }

    @Test
    public void isLineCodesJNTAndDAYTest() {
        Assert.assertTrue(productionLineUtil.isLineCodesJNTAndDAY(productionLineUtil.getJNTAndDAYProductLineCode()));
        Assert.assertFalse(productionLineUtil.isLineCodesJNTAndDAY(Lists.newArrayList(productionLineUtil.getJNTProductLineCode())));
        Assert.assertFalse(productionLineUtil.isLineCodesJNTAndDAY(null));
        Assert.assertFalse(productionLineUtil.isLineCodesJNTAndDAY(Lists.newArrayList()));
    }

    @Test
    public void getDBProductLineCodeTest() {
        Assert.assertTrue(productionLineUtil.getDBProductLineCode(CommonEnum.ProductionLineCodeEnum.DAY.getValue()).intValue() == CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue().intValue());
        Assert.assertTrue(productionLineUtil.getDBProductLineCode(CommonEnum.ProductionLineCodeEnum.JNT.getValue()).intValue() == CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue().intValue());
        Assert.assertTrue(productionLineUtil.getDBProductLineCode(CommonEnum.ProductionLineCodeEnum.RTN.getValue()).intValue() == CommonEnum.UseProductionLineEnum.RTN_TRANSPORTATION.getValue().intValue());
        Assert.assertTrue(productionLineUtil.getDBProductLineCode(CommonEnum.ProductionLineCodeEnum.PTP.getValue()).intValue() == CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue().intValue());
    }

    @Test
    public void getAllIncludeProductionLineListTest() {
        Assert.assertTrue(CollectionUtils.isEmpty(productionLineUtil.getAllIncludeProductionLineList(Lists.newArrayList())));
        Assert.assertTrue(CollectionUtils.isEmpty(productionLineUtil.getAllIncludeProductionLineList(Lists.newArrayList(-1))));
        Assert.assertTrue(CollectionUtils.isEmpty(productionLineUtil.getAllIncludeProductionLineList(Lists.newArrayList(5, 1))));
        Assert.assertTrue(checkListEquals(productionLineUtil.getAllIncludeProductionLineList(Lists.newArrayList(1)), Lists.newArrayList(1, 3, 5, 7, 9, 11, 13, 15)));
        Assert.assertTrue(checkListEquals(productionLineUtil.getAllIncludeProductionLineList(Lists.newArrayList(2)), Lists.newArrayList(2, 3, 6, 7, 10, 11, 14, 15)));
        Assert.assertTrue(checkListEquals(productionLineUtil.getAllIncludeProductionLineList(Lists.newArrayList(3)), Lists.newArrayList(4, 5, 6, 7, 12, 13, 14, 15)));
        Assert.assertTrue(checkListEquals(productionLineUtil.getAllIncludeProductionLineList(Lists.newArrayList(22)), Lists.newArrayList(8, 9, 10, 11, 12, 13, 14, 15)));
        Assert.assertTrue(checkListEquals(productionLineUtil.getAllIncludeProductionLineList(Lists.newArrayList(1, 2)), Lists.newArrayList(3, 7, 11, 15)));
        Assert.assertTrue(checkListEquals(productionLineUtil.getAllIncludeProductionLineList(Lists.newArrayList(1, 3)), Lists.newArrayList(5, 7, 13, 15)));
        Assert.assertTrue(checkListEquals(productionLineUtil.getAllIncludeProductionLineList(Lists.newArrayList(1, 22)), Lists.newArrayList(9, 11, 13, 15)));
        Assert.assertTrue(checkListEquals(productionLineUtil.getAllIncludeProductionLineList(Lists.newArrayList(2, 3)), Lists.newArrayList(6, 7, 14, 15)));
        Assert.assertTrue(checkListEquals(productionLineUtil.getAllIncludeProductionLineList(Lists.newArrayList(2, 22)), Lists.newArrayList(10, 11, 14, 15)));
        Assert.assertTrue(checkListEquals(productionLineUtil.getAllIncludeProductionLineList(Lists.newArrayList(1, 2, 3)), Lists.newArrayList(7, 15)));
        Assert.assertTrue(checkListEquals(productionLineUtil.getAllIncludeProductionLineList(Lists.newArrayList(1, 2, 3, 22)), Lists.newArrayList(15)));
        Assert.assertTrue(checkListEquals(productionLineUtil.getAllIncludeProductionLineList(Lists.newArrayList(1, 2, 3, 4)), Lists.newArrayList()));
    }

    private boolean checkListEquals(List<Integer> a, List<Integer> b) {
        if (CollectionUtils.isEmpty(a) && CollectionUtils.isEmpty(b)) {
            return true;
        }
        if (CollectionUtils.isEmpty(a) && CollectionUtils.isNotEmpty(b)) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(a) && CollectionUtils.isEmpty(b)) {
            return false;
        }
        if (a.size() != b.size()) {
            return false;
        }
        Collections.sort(a);
        Collections.sort(b);
        for (int i = 0; i < a.size(); i++) {
            if (a.get(i).intValue() != b.get(i).intValue()) {
                return false;
            }
        }
        return true;
    }

    private boolean checkStringListEquals(List<String> a, List<String> b) {
        if (a == null && b == null) {
            return true;
        }
        if (a == null && b != null) {
            return false;
        }
        Collections.sort(a);
        Collections.sort(b);
        return JsonUtil.toJson(a).equals(JsonUtil.toJson(b));
    }

    @Test
    public void insideProLineMerge() {
        Set<Integer> lineCodeList = Sets.newHashSet();
        lineCodeList.add(1);
        lineCodeList.add(4);
        Integer line = productionLineUtil.insideProLineMerge(lineCodeList);
        Assert.assertTrue(line == 5);
    }

    @Test
    public void getCategoryCodeListWithEmpty() {
        List<String> categoryCodeList = productionLineUtil.getCategoryCodeList(Lists.newArrayList());
        Assert.assertTrue(categoryCodeList.isEmpty());
    }

    @Test
    public void getCategoryCodeLis() {
        List<Long> categoryIdList = Lists.newArrayList(6L);

        Category category = Category.newBuilder().withCode("airport_pickup").build();
        Mockito.when(categoryRepository.findOne(Mockito.any())).thenReturn(category);
        List<String> categoryCodeList = productionLineUtil.getCategoryCodeList(categoryIdList);
        Assert.assertTrue(categoryCodeList.size() == 1);
        Assert.assertTrue("airport_pickup".equals(categoryCodeList.get(0)));
    }

    @Test
    public void checkBIndTest() throws SQLException {
        TspTransportGroupDriverRelationPO tspTransportGroupDriverRelationPO = new TspTransportGroupDriverRelationPO();
        tspTransportGroupDriverRelationPO.setTransportGroupId(1L);

        Mockito.when(tspTransportGroupDriverRelationRepository.queryTransportGroupIdByDrvIds(Mockito.anyList())).thenReturn(Arrays.asList(tspTransportGroupDriverRelationPO));
        Mockito.when(bindRuleConfig.canBind(Mockito.anyInt(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(true);
        TspTransportGroupPO tspTransportGroupPO = new TspTransportGroupPO();
        tspTransportGroupPO.setCategorySynthesizeCode(1);
        Mockito.when(transportGroupRepository.queryTspTransportByIds(Mockito.anyList())).thenReturn(Arrays.asList(tspTransportGroupPO));
        Result<String> stringResult = productionLineUtil.checkBind(1L, 1, 1);
        Assert.assertTrue(stringResult.isSuccess());
    }

}
