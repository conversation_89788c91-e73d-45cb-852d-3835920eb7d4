package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class TspTransportGroupPOTest {

    @InjectMocks
    TspTransportGroupPO transportGroupPO;

    @Test
    public void test() {
        transportGroupPO.setCategorySynthesizeCode(1);
        Integer integer = transportGroupPO.getCategorySynthesizeCode();
        Assert.assertTrue(integer!=null);
    }

    @Test
    public void test1() {
        transportGroupPO.setStandbyIgtCode("1");
        transportGroupPO.setStandbyPhone("111");
        Assert.assertTrue(true);
    }

}