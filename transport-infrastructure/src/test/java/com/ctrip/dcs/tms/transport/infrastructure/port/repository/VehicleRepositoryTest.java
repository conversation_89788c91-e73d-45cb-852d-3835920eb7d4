package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.google.common.collect.*;
import org.junit.Assert;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.powermock.api.mockito.*;
import org.powermock.core.classloader.annotations.*;
import org.powermock.modules.junit4.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.util.*;

import java.util.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DalQueryDao.class, VehicleRepositoryImpl.class, DalTableDao.class})
@PowerMockIgnore({"javax.management.*","javax.crypto.*"})
public class VehicleRepositoryTest {

    @Autowired
    private VehicleRepository vehicleRepository;

    @Test
    public void getVehVehicleRepo() {
        assert vehicleRepository.getVehVehicleRepo() != null;
    }

    @Test
    public void queryVehicleDetail() {
        assert vehicleRepository.queryVehicleDetail(1L) != null;
    }

    @Test
    public void queryVehicleList() {
        QueryVehicleSOARequestType queryVehicleSOARequestType = new QueryVehicleSOARequestType();
        queryVehicleSOARequestType.setHasDrv(1);
        queryVehicleSOARequestType.setCityId(1);
        PaginatorDTO paginatorDTO = new PaginatorDTO();
        paginatorDTO.setPageNo(1);
        paginatorDTO.setPageSize(10);
        queryVehicleSOARequestType.setPaginator(paginatorDTO);
        assert !CollectionUtils.isEmpty(vehicleRepository.queryVehicleList(queryVehicleSOARequestType,Lists.newArrayList()));
    }

    @Test
    public void queryVehicleCount() {
        QueryVehicleSOARequestType queryVehicleSOARequestType = new QueryVehicleSOARequestType();
        queryVehicleSOARequestType.setCityId(1);
        queryVehicleSOARequestType.setHasDrv(1);
        assert vehicleRepository.queryVehicleCount(queryVehicleSOARequestType,Lists.newArrayList()) >= 0;
    }

    @Test
    public void isVehicleLicenseUniqueness() {
        assert vehicleRepository.isVehicleLicenseUniqueness(1L,"京A224342");
    }

    @Test
    public void checkVehAvailAndHasNoDrv() {
        assert vehicleRepository.checkVehAvailAndHasNoDrv(1L,1L);
    }

    @Test
    public void updateVehicleHasDrv() throws Exception {
        assert vehicleRepository.updateVehicleHasDrv(1L,1,"azhenzhang") >= 0;
    }

    @Test
    public void queryVehVehicleByIds() throws Exception {
        assert CollectionUtils.isEmpty(vehicleRepository.queryVehVehicleByIds(Arrays.asList(1L)));
    }

    @Test
    public void queryDrvBySupplierIdAndIdTest() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl relationRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        List<VehVehiclePO> pos = relationRepository.queryVehVehicleBySupplierIdAndId(Arrays.asList(353935L),10L);
        Assert.assertTrue(pos.size() == 0);
    }

    @Test
    public void countAllVehVehicleByIds() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl relationRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        try {
            int pos = relationRepository.countAllVehVehicleByIds(Arrays.asList(353935L),Arrays.asList(353935L));
            Assert.assertTrue(pos== 0);
        }catch (Exception e){

        }
    }

    @Test
    public void queryAllVehVehicleByIds() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl relationRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        List<VehVehiclePO> pos = relationRepository.queryAllVehVehicleByIds(Arrays.asList(353935L),Arrays.asList(353935L),1,20);
        Assert.assertTrue(pos.size() == 0);
    }
    @Test
    public void queryVehicleId() throws Exception{
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        List<Long> pos = vehicleRepository.queryVehicleId(Arrays.asList("1","2"),1L);
        Assert.assertTrue(pos.size() == 1);
    }

    @Test
    public void updateOfflineVehicleByIdTest() throws Exception {
        VehVehiclePO upPo = new VehVehiclePO();
        upPo.setVehicleId(1L);
        upPo.setVehicleTypeId(112L);

        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalTableDao dao = PowerMockito.mock(DalTableDao.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dao);
        PowerMockito.when(dao.update(Mockito.any(UpdateSqlBuilder.class), Mockito.any(DalHints.class))).thenReturn(1);
        boolean res = vehicleRepository.updateOfflineVehicleById(upPo);
        Assert.assertTrue(res);
    }

}