package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.powermock.api.mockito.*;
import org.powermock.core.classloader.annotations.*;
import org.powermock.modules.junit4.*;


import java.util.*;


@RunWith(PowerMockRunner.class)
@PrepareForTest({DalQueryDao.class, TmsVerifyEventRepositoryImpl.class, DalTableDao.class})
@PowerMockIgnore({"javax.management.*","javax.crypto.*"})
public class TmsVerifyEventRepositoryImplTest {


    @Test
    public void queryWaitVerifyEvent() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsVerifyEventRepositoryImpl relationRepository = new TmsVerifyEventRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeSelectSqlBuilder builder = PowerMockito.mock(FreeSelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        PowerMockito.when(dalRepository.getQueryDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        List<TmsVerifyEventPO> list = relationRepository.queryWaitVerifyEvent(Arrays.asList(1L),1,1,2,true);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void queryVerifyedEvent() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsVerifyEventRepositoryImpl relationRepository = new TmsVerifyEventRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeSelectSqlBuilder builder = PowerMockito.mock(FreeSelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        PowerMockito.when(dalRepository.getQueryDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        List<TmsVerifyEventPO> list = relationRepository.queryVerifyedEvent(1L,1,1);
        Assert.assertTrue(!list.isEmpty());
    }

    @Test
    public void updateVerifyEventByIds() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsVerifyEventRepositoryImpl relationRepository = new TmsVerifyEventRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeUpdateSqlBuilder builder = PowerMockito.mock(FreeUpdateSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        StatementParameters parameters =PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        PowerMockito.when(dalRepository.getQueryDao().update(builder,parameters,hints)).thenReturn(1);
        int i= relationRepository.updateVerifyEventByIds(Arrays.asList(1L),"11","11","11");
        Assert.assertTrue(i == 0);
    }

    @Test
    public void queryVerifyedEventBySourceId() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        TmsVerifyEventRepositoryImpl relationRepository = new TmsVerifyEventRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder sqlBuilder = new SelectSqlBuilder();
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        PowerMockito.when(dalRepository.getDao().query(sqlBuilder,hints)).thenReturn(Lists.newArrayList());
        List<TmsVerifyEventPO> list = relationRepository.queryVerifyedEventBySourceId(1L,1,2);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void queryVerifyedEventBySourceIdOneMonth() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);

        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DalHints dalHints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder sqlBuilder = new SelectSqlBuilder();
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalTableDao.query(sqlBuilder,dalHints)).thenReturn(Lists.newArrayList(new TmsVerifyEventPO()));
        TmsVerifyEventRepositoryImpl tmsVerifyEventRepository = new TmsVerifyEventRepositoryImpl();
        List<TmsVerifyEventPO> tmsVerifyEventPOS = tmsVerifyEventRepository.queryVerifyedEventBySourceIdOneMonth(1L, 1, 2);
        Assert.assertTrue(tmsVerifyEventPOS.size()==0);
    }
}
