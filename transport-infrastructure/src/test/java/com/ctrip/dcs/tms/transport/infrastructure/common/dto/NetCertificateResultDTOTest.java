package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import com.ctrip.dcs.tms.transport.api.model.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class NetCertificateResultDTOTest {

    @InjectMocks
    NetCertificateResultDTO resultDTO;

    @Test
    public void getData() {
        QueryNetDrvCardInfoSOAResponseType soaResponseType = resultDTO.getData();
        Assert.assertTrue(soaResponseType == null);
    }

    @Test
    public void setData() {
        resultDTO.setData(new QueryNetDrvCardInfoSOAResponseType());
        Assert.assertTrue(true);
    }
}
