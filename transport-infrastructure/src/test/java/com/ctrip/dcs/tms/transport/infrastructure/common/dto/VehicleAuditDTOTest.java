package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class VehicleAuditDTOTest {

    @InjectMocks
    VehicleAuditDTO vehicleAuditDTO;

    @Test
    public void getNewVehicleLicense() {
        String str = vehicleAuditDTO.getNewVehicleLicense();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setNewVehicleLicense() {
        vehicleAuditDTO.setNewVehicleLicense("222");
        Assert.assertTrue(true);
    }

    @Test
    public void getId() {
        Long long1 = vehicleAuditDTO.getId();
        Assert.assertTrue(long1 == null);
    }

    @Test
    public void setId() {
        vehicleAuditDTO.setId(1L);
        Assert.assertTrue(true);
    }

    @Test
    public void getUserName() {
        String str = vehicleAuditDTO.getUserName();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setUserName() {
        vehicleAuditDTO.setUserName("1");
        Assert.assertTrue(true);
    }

    @Test
    public void getUseType() {
        String str = vehicleAuditDTO.getUseType();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setUseType() {
        vehicleAuditDTO.setUseType("1");
        Assert.assertTrue(true);
    }

    @Test
    public void getVehicleLicense() {
        String str = vehicleAuditDTO.getVehicleLicense();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setVehicleLicense() {
        vehicleAuditDTO.setVehicleLicense("1");
        Assert.assertTrue(true);
    }

    @Test
    public void getVehicleLicenseType() {
        String str = vehicleAuditDTO.getVehicleLicenseType();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setVehicleLicenseType() {
        vehicleAuditDTO.setVehicleLicenseType("02");
        Assert.assertTrue(true);
    }

    @Test
    public void getCheckId() {
        Long long1 = vehicleAuditDTO.getCheckId();
        Assert.assertTrue(long1 == null);
    }

    @Test
    public void setCheckId() {
        vehicleAuditDTO.setCheckId(1L);
        Assert.assertTrue(true);
    }

    @Test
    public void getVinCode() {
        String str = vehicleAuditDTO.getVinCode();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setVinCode() {
        vehicleAuditDTO.setVinCode("2");
        Assert.assertTrue(true);
    }

    @Test
    public void builder() {
        vehicleAuditDTO.builder();
        Assert.assertTrue(true);
    }

    @Test
    public void getCheckType() {
        vehicleAuditDTO.getCheckType();
        Assert.assertTrue(true);
    }

    @Test
    public void setCheckType() {
        vehicleAuditDTO.setCheckType(1);
        Assert.assertTrue(true);
    }

    @Test
    public void getCertificateType() {
        Integer integer = vehicleAuditDTO.getCertificateType();
        Assert.assertTrue(integer == null);
    }

    @Test
    public void setCertificateType() {
        vehicleAuditDTO.setCertificateType(1);
        Assert.assertTrue(true);
    }

    @Test
    public void getOriginNetTansCtfctUrl() {
        String str = vehicleAuditDTO.getOriginNetTansCtfctUrl();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setOriginNetTansCtfctUrl() {
        vehicleAuditDTO.setOriginNetTansCtfctUrl("1");
        Assert.assertTrue(true);
    }

    @Test
    public void getTarNetTansCtfctUrl() {
        String str = vehicleAuditDTO.getTarNetTansCtfctUrl();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setTarNetTansCtfctUrl() {
        vehicleAuditDTO.setTarNetTansCtfctUrl("122");
        Assert.assertTrue(true);
    }

    @Test
    public void getOriginVehicleCertiImg() {
        String str = vehicleAuditDTO.getOriginVehicleCertiImg();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setOriginVehicleCertiImg() {
        vehicleAuditDTO.setOriginVehicleCertiImg("1");
        Assert.assertTrue(true);
    }

    @Test
    public void getTarVehicleCertiImg() {
        String str =  vehicleAuditDTO.getTarVehicleCertiImg();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setTarVehicleCertiImg() {
        vehicleAuditDTO.setTarVehicleCertiImg("1");
        Assert.assertTrue(true);
    }

    @Test
    public void updateBuildVehicleDTO() {
        VehVehiclePO vehiclePO = new VehVehiclePO();
        vehiclePO.setVehicleId(1L);
        VehVehiclePO vehiclePO1 = new VehVehiclePO();
        vehiclePO1.setVehicleId(1L);
        vehicleAuditDTO.updateBuildVehicleDTO(vehiclePO,vehiclePO1,1);
        Assert.assertTrue(true);
    }

    @Test
    public void rehBuildVehicleDTO() {

        vehicleAuditDTO.rehBuildVehicleDTO("1","02","3",1L,"system",1,1L,1,1,null,null);
        Assert.assertTrue(true);
    }
    @Test
    public void refreshAllVehicleDTO() {
        VehVehiclePO vehiclePO = new VehVehiclePO();
        vehiclePO.setVehicleId(1L);
        vehicleAuditDTO.refreshAllVehicleDTO(vehiclePO);
        Assert.assertTrue(true);
    }
    @Test
    public void approveVehicleDTO() {
        VehVehiclePO vehiclePO = new VehVehiclePO();
        vehiclePO.setVehicleId(1L);
        vehiclePO.setVehicleEnergyType(1);
        VehVehiclePO newvehiclePO = new VehVehiclePO();
        newvehiclePO.setVehicleId(1L);
        vehicleAuditDTO.approveVehicleDTO(vehiclePO,newvehiclePO);
        Assert.assertTrue(true);
    }
    @Test
    public void rehApproveBuildVehicleDTO() {
        vehicleAuditDTO.rehApproveBuildVehicleDTO("1","1");
        Assert.assertTrue(true);
    }
}
