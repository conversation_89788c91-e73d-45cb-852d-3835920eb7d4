package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.impl

import com.ctrip.dcs.driver.domain.account.AccountDetailDTO
import com.ctrip.dcs.driver.domain.account.DriverUDL
import com.ctrip.dcs.driver.domain.account.GenerateGlobalIdRequestType
import com.ctrip.dcs.driver.domain.account.GenerateGlobalIdResponseType
import com.ctrip.dcs.driver.domain.account.ParsePhoneNumberResponseType
import com.ctrip.dcs.driver.domain.account.QueryAccountByEmailRequestType
import com.ctrip.dcs.driver.domain.account.QueryAccountByEmailResponseType
import com.ctrip.dcs.driver.domain.account.QueryAccountByMobilePhoneRequestType
import com.ctrip.dcs.driver.domain.account.QueryAccountByMobilePhoneResponseType
import com.ctrip.dcs.driver.domain.account.QueryAccountBySourceResponseType
import com.ctrip.dcs.driver.domain.account.QueryAccountProcessResponseType
import com.ctrip.dcs.driver.domain.account.QueryUDLByDriverIdResponseType
import com.ctrip.dcs.driver.domain.account.RegisterAccountResponseType
import com.ctrip.dcs.driver.domain.account.RegisterNewAccountRequestType
import com.ctrip.dcs.driver.domain.account.RegisterNewAccountResponseType
import com.ctrip.dcs.driver.domain.account.SyncAccountRequestType
import com.ctrip.dcs.driver.domain.account.SyncAccountResponseType
import com.ctrip.dcs.driver.domain.account.UpdateAccountRequestType
import com.ctrip.dcs.driver.domain.account.UpdateAccountResponseType
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.DriverAccountDetail
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO
import com.ctrip.dcs.tms.transport.infrastructure.common.converter.DriverDomainConverter
import com.ctrip.dcs.tms.transport.infrastructure.common.converter.DriverGuideConverter
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverAccountRegisterResultDTO
import com.ctrip.dcs.tms.transport.infrastructure.common.udl.dto.DriverUdlInfo
import com.ctrip.igt.ResponseResult
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.igt.framework.common.result.Result
import org.junit.runner.RunWith
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.*

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest([DriverGuideConverter, Result.Builder])
@SuppressStaticInitializationFor(["com.ctrip.dcs.tms.transport.infrastructure.common.converter.DriverGuidConverter", "com.ctrip.dcs.tms.transport.infrastructure.common.converter.DriverDomainConverter", "com.ctrip.igt.framework.common.result.Result.Builder"])
class DriverDomainServiceProxyImplTest extends Specification {
    def testObj = new DriverDomainServiceProxyImpl()
    def driverDomainService = Mock(DriverDomainService)
    def driverGuideConverter = Mock(DriverGuideConverter)

    def setup() {

        testObj.driverDomainService = driverDomainService
        testObj.driverGuideConverter = driverGuideConverter
    }

    @Unroll
    def "registerAccountGetUidTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.doRegisterAccountGetUid(_, _, _, _, _) >> new DriverAccountRegisterResultDTO()
        when:
        def result = spy.registerAccountGetUid(drvId, drvPhone, email, igtCode, dpwd)

        then: "验证返回结果里属性值是否符合预期"
        result.toString() == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvId | drvPhone   | dpwd   | igtCode   | email   || expectedResult
        1L    | "drvPhone" | "dpwd" | "igtCode" | "email" || new DriverAccountRegisterResultDTO().toString()
    }

    @Unroll
    def "queryAccountProcessTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainService.queryAccountProcess(_) >> new QueryAccountProcessResponseType(isNewAccount: true, responseResult: new ResponseResult(success: true))

        when:
        def result = testObj.queryAccountProcess(cityId, driverId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        driverId | cityId || expectedResult
        1L       | 1L     || true
    }

    @Unroll
    def "registerAccountGetUidTest2"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.doRegisterAccountGetUid(_, _, _, _, _) >> new DriverAccountRegisterResultDTO(uid: "uid")
        when:
        def result = spy.registerAccountGetUid(drvId, drvPhone, email, igtCode, isJob)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvId | drvPhone   | igtCode   | email   | isJob        || expectedResult
        1L    | "drvPhone" | "igtCode" | "email" | Boolean.TRUE || "uid"
    }

    @Unroll
    def "doRegisterAccountGetUidTest3"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainService.registerAccount(_) >> new RegisterAccountResponseType(responseResult: new ResponseResult(success: true, returnCode: "errorCode", returnMessage: "errorMsg"), uid: null)

        when:
        def result = testObj.doRegisterAccountGetUid(drvId, drvPhone, email, igtCode, isJob)

        then: "验证返回结果里属性值是否符合预期"
        result.toString() == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvId | drvPhone   | igtCode   | email   | isJob        || expectedResult
        1L    | "drvPhone" | "igtCode" | "email" | Boolean.TRUE || new DriverAccountRegisterResultDTO(drvId: 1L, needRegisterAccount: true, registerSuccess: false, errorCode: "errorCode", errorMsg: "errorMsg", uid: "").toString()
    }

    @Unroll
    def "syncAccountTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainService.syncAccount(_) >> syncAccountResponseType

        and: "Mock 静态方法"
        mockStatic(DriverGuideConverter)
        DriverGuideConverter.buildSyncAccountRequestType(_) >> new SyncAccountRequestType()
        when:
        def result = testObj.syncAccount(request)

        then: "验证返回结果里属性值是否符合预期"
        result.getData().getUid() == expectedResult
        where: "表格方式验证多种分支调用场景"
        request                                || syncAccountResponseType                                                                                                                              || expectedResult
        new DrvDriverPO(uid: "uid", drvId: 1L) || new SyncAccountResponseType(responseResult: new ResponseResult(success: true, returnCode: "returnCode", returnMessage: "returnMessage"), uid: "uid") || "uid"
        new DrvDriverPO(uid: "uid", drvId: 1L) || new SyncAccountResponseType(responseResult: new ResponseResult(success: false, returnCode: "returnCode", returnMessage: "returnMessage"), uid: "")   || ""
    }

    @Unroll
    def "updateAccountTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainService.updateAccount(_) >> new UpdateAccountResponseType(responseResult: new ResponseResult(success: true, returnCode: "returnCode", returnMessage: "returnMessage"))

        and: "Mock 静态方法"
        driverGuideConverter.buildUpdateAccountRequestType(_) >> (new UpdateAccountRequestType())
        when:
        def result = testObj.updateAccount(request)

        then: "验证返回结果里属性值是否符合预期"
        result.isSuccess() == expectedResult
        where: "表格方式验证多种分支调用场景"
        request                                || expectedResult
        new DrvDriverPO(drvId: 1L)             || true
        new DrvDriverPO(drvId: 1L, uid: "uid") || true
    }

    @Unroll
    def "updateAccountTestReturnFailure"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainService.updateAccount(_) >> new UpdateAccountResponseType(responseResult: new ResponseResult(success: false, returnCode: "returnCode", returnMessage: "returnMessage"))

        and: "Mock 静态方法"
        driverGuideConverter.buildUpdateAccountRequestType(_) >> (new UpdateAccountRequestType())
        when:
        def result = testObj.updateAccount(request)

        then: "验证返回结果里属性值是否符合预期"
        result.isSuccess() == expectedResult
        where: "表格方式验证多种分支调用场景"
        request                                || expectedResult
        new DrvDriverPO(drvId: 1L, uid: "uid") || false
    }

    @Unroll
    def "registerNewAccountTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainService.registerNewAccount(_) >> registerNewAccountRsp

        and: "Mock 静态方法"
        driverGuideConverter.buildUpdateAccountRequestType(_) >> (new UpdateAccountRequestType())
        when:
        def result = testObj.registerNewAccount(request)

        then: "验证返回结果里属性值是否符合预期"
        result.toString() == expectedResult
        where: "表格方式验证多种分支调用场景"
        request                                                                                        || registerNewAccountRsp                                                                                                                                    || expectedResult
        new DrvDriverPO(uid: "uid", drvId: 1L, ppmAccount: "ppmAccount", qunarAccount: "qunarAccount") || new RegisterNewAccountResponseType(responseResult: new ResponseResult(success: true, returnCode: "200"), uid: "uid", ppmAccountId: "ppmAccountId")       || new DriverAccountRegisterResultDTO(drvId: 1L, uid: "uid", needRegisterAccount: true, registerSuccess: true, errorCode: "200", ppmAccount: "ppmAccountId", qunarAccount: "ppmAccountId").toString()
        new DrvDriverPO(uid: "uid", drvId: 1L, ppmAccount: "ppmAccount", qunarAccount: "qunarAccount") || new RegisterNewAccountResponseType(responseResult: new ResponseResult(success: false, returnCode: "errorCode"), uid: null, ppmAccountId: "ppmAccountId") || new DriverAccountRegisterResultDTO(drvId: 1L, uid: "", needRegisterAccount: true, registerSuccess: false, errorCode: "errorCode").toString()
    }

    @Unroll
    def "generateGlobalIdTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainService.generateGlobalId(_) >> new GenerateGlobalIdResponseType(responseResult: new ResponseResult(success: true, returnCode: "returnCode", returnMessage: "returnMessage"), globalId: 1L)

        and: "Mock 静态方法"
        driverGuideConverter.buildGenerateGlobalIdRequestType(_) >> (new GenerateGlobalIdRequestType())
        when:
        def result = testObj.generateGlobalId(request)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        request                    || expectedResult
        new DrvDriverPO(drvId: 1L) || null
    }

    @Unroll
    def "generateGlobalIdThrowExceptionTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainService.generateGlobalId(_) >> new GenerateGlobalIdResponseType(responseResult: new ResponseResult(success: false, returnCode: "returnCode", returnMessage: "returnMessage"), globalId: 1L)

        and: "Mock 静态方法"
        driverGuideConverter.buildGenerateGlobalIdRequestType(_) >> (new GenerateGlobalIdRequestType())
        when:
        testObj.generateGlobalId(request)
        then:
        def ex = thrown(BizException)
        ex.getCode() == expectedResult

        where: "表格方式验证多种分支调用场景"
        request                    || expectedResult
        new DrvDriverPO(drvId: 1L) || "returnCode"
    }

    @Unroll
    def "queryAccountBySourceTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainService.queryAccountBySource(_) >> new QueryAccountBySourceResponseType(responseResult: new ResponseResult(success: true, returnCode: "returnCode", returnMessage: "returnMessage"), accountDetail: new AccountDetailDTO())

        and: "Mock 静态方法"
        mockStatic(DriverDomainConverter)
        when(DriverDomainConverter.buildAccount2DrvDriverPO(anyLong(), any())).thenReturn(new DrvDriverPO())
        when:
        def result = testObj.queryAccountBySource(drvId)

        then: "验证返回结果里属性值是否符合预期"
        result.isSuccess() == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvId || expectedResult
        1L    || true
    }

    @Unroll
    def "queryAccountBySourceReturnFailureTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainService.queryAccountBySource(_) >> new QueryAccountBySourceResponseType(responseResult: new ResponseResult(success: false, returnCode: "returnCode", returnMessage: "returnMessage"), accountDetail: new AccountDetailDTO())

        and: "Mock 静态方法"
        mockStatic(DriverDomainConverter)
        when(DriverDomainConverter.buildAccount2DrvDriverPO(anyLong(), any())).thenReturn(new DrvDriverPO())
        when:
        def result = testObj.queryAccountBySource(drvId)

        then: "验证返回结果里属性值是否符合预期"
        result.isSuccess() == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvId || expectedResult
        1L    || false
    }

    @Unroll
    def "parsePhoneNumberTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainService.parsePhoneNumber(_) >> parsePhoneNumberResponseType

        when:
        def result = testObj.parsePhoneNumber(request)

        then: "验证返回结果里属性值是否符合预期"
        result.isSuccess() == expectedResult
        where: "表格方式验证多种分支调用场景"
        request || parsePhoneNumberResponseType                         || expectedResult
        null    || new ParsePhoneNumberResponseType(validNumber: true)  || true
        null    || new ParsePhoneNumberResponseType(validNumber: false) || false
    }

    @Unroll
    def "queryAccountByMobilePhoneTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainService.queryAccountByMobilePhone(_) >> new QueryAccountByMobilePhoneResponseType(accountDetail: new AccountDetailDTO())

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.convert(_) >> new DriverAccountDetail()
        when:
        def result = spy.queryAccountByMobilePhone(request)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        request                                    || expectedResult
        new QueryAccountByMobilePhoneRequestType() || new DriverAccountDetail()
    }

    @Unroll
    def "convertTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.convert(accountDetail)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        accountDetail                                  || expectedResult
        new AccountDetailDTO(uid: "uid", name: "name") || new DriverAccountDetail(uid: "uid", name: "name")
    }

    @Unroll
    def "queryAccountByEmailTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainService.queryAccountByEmail(_) >> new QueryAccountByEmailResponseType(accountDetail: new AccountDetailDTO())

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.convert(_) >> new DriverAccountDetail()
        when:
        def result = spy.queryAccountByEmail(request)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        request                              || expectedResult
        new QueryAccountByEmailRequestType() || new DriverAccountDetail()
    }

    @Unroll
    def "queryUDLByDriverIdTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainService.queryUDLByDriverId(_) >> new QueryUDLByDriverIdResponseType(driverUdL: [new DriverUDL(udl: "udl", uid: "uid")])

        when:
        def result = testObj.queryUDLByDriverId(drvId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvId || expectedResult
        1L    || new DriverUdlInfo(udl: "udl", uid: "uid")
    }

}
