package com.ctrip.dcs.tms.transport.infrastructure.common.util

import spock.lang.Specification

/**
 * Created by IntelliJ IDEA.
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @create 2024/2/22 15:22
 */
class SharkUtilsGroovy extends Specification {

    def "test getBaseSharkValue"() {
        given:
        def key = "word"
        when:
        def res = SharkUtils.getBaseSharkValue(key)
        then:
        res != null
    }

    def "test getSharkValue"() {
        given:
        def key = "word"
        when:
        def res = SharkUtils.getSharkValue(key)
        then:
        res != null
    }

    def "test getSharkValue 1"() {
        given:
        def key = "word"
        when:
        def res = SharkUtils.getSharkValue(key, "111")
        then:
        res != null
    }

    def "test getSharkValue 2"() {
        given:
        def key = "word"
        when:
        def res = SharkUtils.getSharkValue(key, "111")
        then:
        res != null
    }

    def "test getSharkValue 3"() {
        given:
        def key = "word"
        when:
        def res = SharkUtils.getSharkValue(key, 111)
        then:
        res != null
    }

    def "test getSharkValueDefault"() {
        given:
        def key = "word"
        when:
        def res = SharkUtils.getSharkValue(key, key)
        then:
        res != null
    }

}