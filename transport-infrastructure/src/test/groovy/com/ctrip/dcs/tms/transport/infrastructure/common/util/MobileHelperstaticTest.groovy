package com.ctrip.dcs.tms.transport.infrastructure.common.util

import com.ctrip.arch.coreinfo.enums.KeyType
import com.ctrip.dcs.geo.domain.repository.CityRepository
import com.ctrip.dcs.geo.domain.value.City
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ErrorCodeEnum
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.BusinessQConfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig
import com.ctrip.igt.framework.common.result.Result
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants
import org.junit.runner.RunWith
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import spock.lang.Unroll

import static org.powermock.api.mockito.PowerMockito.mockStatic
import static org.powermock.api.mockito.PowerMockito.when

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest([TmsTransUtil, SharkUtils, Result.Builder])
@SuppressStaticInitializationFor(["com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil", "com.ctrip.dcs.tms.transport.infrastructure.common.util.SharkUtils", "com.ctrip.igt.framework.common.result.Result.Builder"])
class MobileHelperstaticTest extends Specification {
    def testObj = new MobileHelper()
    def tmsTransportQconfig = Mock(TmsTransportQconfig)
    def cityRepository = Mock(CityRepository)
    def businessQConfig = Mock(BusinessQConfig)

    def setup() {

        testObj.tmsTransportQconfig = tmsTransportQconfig
        testObj.cityRepository = cityRepository
        testObj.businessQConfig = businessQConfig
    }



    @Unroll
    def "isMobileValidMobileDecriptTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tmsTransportQconfig.getCheckMobileSwitch() >> Boolean.TRUE
        businessQConfig.isOpen() >> Boolean.TRUE

        mockStatic(TmsTransUtil)
        when(TmsTransUtil.decrypt("0=245", KeyType.Phone)).thenReturn("1234")

        when:
        def result = testObj.isMobileValid(igtCode, phone, 1)

        then: "验证返回结果里属性值是否符合预期"
        result.getCode() == expectedResult
        where: "表格方式验证多种分支调用场景"
        phone   | igtCode   || expectedResult
        "0=245" | "igtCode" || ServiceResponseConstants.ResStatus.SUCCESS_CODE
    }

}
