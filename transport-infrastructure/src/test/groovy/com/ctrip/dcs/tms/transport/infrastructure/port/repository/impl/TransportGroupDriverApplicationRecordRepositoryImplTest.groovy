//package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl
//
//import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TransportGroupDriverApplicationRecordPO
//import com.ctrip.dcs.tms.transport.infrastructure.common.dto.TransportGroupDriverApplyProcessDTO
//import com.ctrip.platform.dal.dao.DalHints
//import com.ctrip.platform.dal.dao.base.DalTableOperations
//import spock.lang.Specification
//import spock.lang.Unroll
//
//class TransportGroupDriverApplicationRecordRepositoryImplTest extends Specification {
//    def testObj = new TransportGroupDriverApplicationRecordRepositoryImpl()
//    def dalTableOperations = Mock(DalTableOperations)
//
//    def setup() {
//
//        testObj.dalTableOperations = dalTableOperations
//    }
//
//    @Unroll
//    def "queryByConditionTest"() {
//        given: "设定相关方法入参"
//        and: "Mock相关接口返回"
//        dalTableOperations.queryBy(_, _) >> [new TransportGroupDriverApplicationRecordPO()]
//
//        when:
//        def result = testObj.queryByCondition(condition)
//
//        then: "验证返回结果里属性值是否符合预期"
//        result.size() == expectedResult
//        where: "表格方式验证多种分支调用场景"
//        condition                                     || expectedResult
//        new TransportGroupDriverApplicationRecordPO() || 1
//    }
//
////    @Unroll
////    def "batchInsertTest"() {
////        given: "设定相关方法入参"
////        and: "Mock相关接口返回"
////        dalTableOperations.batchInsert(_ as DalHints, _ as List<TransportGroupDriverApplicationRecordPO>) >> [0] as int[]
////
////        when:
////        def result = testObj.batchInsert(recordList as List<TransportGroupDriverApplicationRecordPO>)
////
////        then: "验证返回结果里属性值是否符合预期"
////        result == expectedResult
////        where: "表格方式验证多种分支调用场景"
////        recordList                                      || expectedResult
////        [new TransportGroupDriverApplicationRecordPO()] || null
////    }
//
//    @Unroll
//    def "updateTest"() {
//        given: "设定相关方法入参"
//        when:
//        def result = testObj.update(record)
//
//        then: "验证返回结果里属性值是否符合预期"
//        result == expectedResult
//        where: "表格方式验证多种分支调用场景"
//        record                                        || expectedResult
//        new TransportGroupDriverApplicationRecordPO() || null
//    }
//
//    @Unroll
//    def "updateHistoryData2InactiveTest"() {
//        given: "设定相关方法入参"
//        and: "Mock相关接口返回"
//
//        and: "Spy相关接口"
//        def spy = Spy(testObj)
//        spy.doUpdateHistoryData2Inactive(_, _, _) >> {}
//        when:
//        def result = spy.updateHistoryData2Inactive(transportGroupId, workShiftId)
//
//        then: "验证返回结果里属性值是否符合预期"
//        result == expectedResult
//        where: "表格方式验证多种分支调用场景"
//        transportGroupId | workShiftId || expectedResult
//        1L               | 1L          || null
//    }
//
//    @Unroll
//    def "updateHistoryData2InactiveTest1"() {
//        given: "设定相关方法入参"
//        and: "Mock相关接口返回"
//
//        and: "Spy相关接口"
//        def spy = Spy(testObj)
//        spy.doUpdateHistoryData2Inactive(_, _, _) >> {}
//        when:
//        def result = spy.updateHistoryData2Inactive(transportGroupId, drvIdList)
//
//        then: "验证返回结果里属性值是否符合预期"
//        result == expectedResult
//        where: "表格方式验证多种分支调用场景"
//        drvIdList | transportGroupId || expectedResult
//        [1L]      | 1L               || null
//    }
//
//    @Unroll
//    def "doUpdateHistoryData2InactiveTest"() {
//        given: "设定相关方法入参"
//        and: "Mock相关接口返回"
//        dalTableOperations.update(_, _, _) >> 0
//
//        when:
//        def result = testObj.doUpdateHistoryData2Inactive(transportGroupId, drvIdList, workShiftId)
//
//        then: "验证返回结果里属性值是否符合预期"
//        result == expectedResult
//        where: "表格方式验证多种分支调用场景"
//        drvIdList | transportGroupId | workShiftId || expectedResult
//        [1L]      | 1L               | 1L          || null
//    }
//}
