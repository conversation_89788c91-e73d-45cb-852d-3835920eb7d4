package com.ctrip.dcs.tms.transport.infrastructure.common.util

import credis.java.client.CacheProvider
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.*

class RedisUtilsSpockTest extends Specification {
    def testObj = new RedisUtils(null)
    def provider = Mock(TmsServiceProvider)
    def cacheProvider = Mock(CacheProvider)

    def setup() {

        testObj.provider = provider
    }

    @Unroll
    def "getIntegerTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        provider.getProvider() >> null

        when:
        def result = RedisUtils.getInteger(key)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        key   || expectedResult
        "key" || null
    }

    @Unroll
    def "setStringTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        provider.getProvider() >> cacheProvider

        when:
        def result = RedisUtils.setString(key, exp, o)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        exp | key   | o   || expectedResult
        0   | "key" | "o" || true
    }
}
