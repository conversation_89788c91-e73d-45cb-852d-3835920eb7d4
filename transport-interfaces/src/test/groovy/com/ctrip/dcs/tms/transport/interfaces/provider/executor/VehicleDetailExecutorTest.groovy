package com.ctrip.dcs.tms.transport.interfaces.provider.executor

import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOARequestType
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOAResponseType
import com.ctrip.dcs.tms.transport.interfaces.bridge.ProductLineBridgeManagement
import spock.lang.*
import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.*


class VehicleDetailExecutorTest extends Specification {
    def testObj = new VehicleDetailExecutor()
    def productLineBridgeManagement = Mock(ProductLineBridgeManagement)

    def setup() {

        testObj.productLineBridgeManagement = productLineBridgeManagement
    }


    @Unroll
    def "executeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        productLineBridgeManagement.queryVehicleDetail(_) >> new VehicleDetailSOAResponseType()


        when:
        def result = testObj.execute(vehicleDetailSOARequestType)

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        vehicleDetailSOARequestType       || expectedResult
        new VehicleDetailSOARequestType() || new VehicleDetailSOAResponseType()
    }


}

