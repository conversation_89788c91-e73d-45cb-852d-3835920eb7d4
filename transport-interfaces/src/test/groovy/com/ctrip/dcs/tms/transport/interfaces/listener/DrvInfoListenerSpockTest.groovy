package com.ctrip.dcs.tms.transport.interfaces.listener

import com.ctrip.dcs.tms.transport.application.command.CommonCommandService
import com.ctrip.dcs.tms.transport.application.command.DriverCommandService
import com.ctrip.dcs.tms.transport.application.command.PushDataCommandService
import com.ctrip.dcs.tms.transport.application.command.TmsQmqProducerCommandService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainServiceProxy
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository
import com.ctrip.igt.framework.common.result.Result
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification
import spock.lang.Unroll

class DrvInfoListenerSpockTest extends Specification {
    def testObj = new DrvInfoListener()
    def commonCommandService = Mock(CommonCommandService)
    def drvierRepository = Mock(DrvDrvierRepository)
    def driverCommandService = Mock(DriverCommandService)
    def transportGroupRepository = Mock(TransportGroupRepository)
    def pushDataCommandService = Mock(PushDataCommandService)
    def domainServiceProxy = Mock(DriverDomainServiceProxy)
    def qmqProducerCommandService = Mock(TmsQmqProducerCommandService)

    def setup() {

        testObj.transportGroupRepository = transportGroupRepository
        testObj.pushDataCommandService = pushDataCommandService
        testObj.driverCommandService = driverCommandService
        testObj.drvierRepository = drvierRepository
        testObj.domainServiceProxy = domainServiceProxy
        testObj.commonCommandService = commonCommandService
        testObj.qmqProducerCommandService = qmqProducerCommandService
    }

    @Unroll
    def "driverAccountBaseInfoUpdateTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        drvierRepository.updateDrv(_) >> count
        domainServiceProxy.queryAccountBySource(_) >> Result.Builder.<DrvDriverPO>newResult().success().withData(new DrvDriverPO()).build()

        when:
        def result = testObj.driverAccountBaseInfoUpdate(message)


        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        count | message || expectedResult
        0|new BaseMessage(messageId:"123", subject: TmsTransportConstant.QmqSubject.SUBJECT_DRIVER_START_SERVICE, attrs: ["content":"{\"uid\":\"TODG_1nrbxsmkfknc\",\"updateSource\":\"driver\",\"accountIdentitySourceList\":[{\"source\":\"DriverGuid\",\"sourceId\":\"3455215\"},{\"source\":\"Driver\",\"sourceId\":\"3455215\"}]}"],) || null
        1|new BaseMessage(messageId:"123", subject: TmsTransportConstant.QmqSubject.SUBJECT_DRIVER_START_SERVICE, attrs: ["content":"{\"uid\":\"TODG_1nrbxsmkfknc\",\"updateSource\":\"driver\",\"accountIdentitySourceList\":[{\"source\":\"DriverGuid\",\"sourceId\":\"3455215\"},{\"source\":\"Driver\",\"sourceId\":\"3455215\"}]}"],) || null
    }
}
