package com.ctrip.dcs.tms.transport.interfaces.listener

import base.BaseTest
import com.ctrip.dcs.tms.transport.application.command.CommonCommandService
import com.ctrip.dcs.tms.transport.application.command.DriverCommandService
import com.ctrip.dcs.tms.transport.application.command.VehicleCommandService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverseasQconfig
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository

class ApproveCertificateCheckListenerTest extends BaseTest {

    def  drvDrvierRepository = Mock(DrvDrvierRepository)
    def driverCommandService = Mock(DriverCommandService)
    def vehicleRepository = Mock(VehicleRepository)
    def vehicleCommandService = Mock(VehicleCommandService)
    def commonService = Mock(CommonCommandService)
    def enumService = Mock(EnumRepository)
    def oversaasQconfig = Mock(OverseasQconfig)
    def approveCertificateCheckListener = new ApproveCertificateCheckListener(drvDrvierRepository: drvDrvierRepository,
            driverCommandService: driverCommandService,vehicleRepository:vehicleRepository,vehicleCommandService:vehicleCommandService,commandService: commonService,
    enumRepository: enumService,overseasQconfig: oversaasQconfig)

    def "test drvApproveTemporaryToOfficial"(){
        given:
        def drvdriverPO = new DrvDriverPO()
        drvdriverPO.setTemporaryDispatchMark(1)
        drvdriverPO.setDrvcardImg("1111")
        drvDrvierRepository.queryByPk(_) >> drvdriverPO
        drvDrvierRepository.updateTemporaryDispatchMark(_, _, _) >> 1
        driverCommandService.temToOfficialSendQmq(_, _, _) >> true
        when:
        def res = approveCertificateCheckListener.drvApproveTemporaryToOfficial(1L,"11")
        then:
        res!=null
    }

    def "test drvApproveTemporaryToOfficial 2"(){
        given:
        def drvdriverPO = new DrvDriverPO()
        drvdriverPO.setTemporaryDispatchMark(0)
        drvdriverPO.setDrvcardImg("1111")
        drvDrvierRepository.queryByPk(_) >> drvdriverPO
        when:
        def res = approveCertificateCheckListener.drvApproveTemporaryToOfficial(1L,"11")
        then:
        res!=null
    }

    def "test drvApproveTemporaryToOfficial 3"(){
        given:
        def drvdriverPO = new DrvDriverPO()
        drvdriverPO.setTemporaryDispatchMark(1)
        drvDrvierRepository.queryByPk(_) >> drvdriverPO
        when:
        def res = approveCertificateCheckListener.drvApproveTemporaryToOfficial(1L,"11")
        then:
        res!=null
    }

    def "test drvApproveTemporaryToOfficial 4"(){
        given:
        def drvdriverPO = new DrvDriverPO()
        drvdriverPO.setTemporaryDispatchMark(1)
        drvdriverPO.setDrvcardImg("1111")
        drvDrvierRepository.queryByPk(_) >> drvdriverPO
        drvDrvierRepository.updateTemporaryDispatchMark(_, _, _) >> 0
        when:
        def res = approveCertificateCheckListener.drvApproveTemporaryToOfficial(1L,"11")
        then:
        res!=null
    }

    def "test vehApproveTemporaryToOfficial"(){
        given:
        def vehVehiclePO = new VehVehiclePO()
        vehVehiclePO.setTemporaryDispatchMark(1)
        vehicleRepository.queryByPk(_) >> vehVehiclePO
        vehicleRepository.updateTemporaryDispatchMark(_, _, _) >> 1
        vehicleCommandService.temToOfficialSendQmq(_, _, _) >> true
        when:
        def res = approveCertificateCheckListener.vehApproveTemporaryToOfficial(1L,"11")
        then:
        res!=null
    }

    def "test vehApproveTemporaryToOfficial 2"(){
        given:
        def vehVehiclePO = new VehVehiclePO()
        vehVehiclePO.setTemporaryDispatchMark(0)
        vehVehiclePO.setVehicleFullImg("111")
        vehicleRepository.queryByPk(_) >> vehVehiclePO
        when:
        def res = approveCertificateCheckListener.vehApproveTemporaryToOfficial(1L,"11")
        then:
        res!=null
    }

    def "test vehApproveTemporaryToOfficial 3"(){
        given:
        def vehVehiclePO = new VehVehiclePO()
        vehVehiclePO.setTemporaryDispatchMark(1)
        vehVehiclePO.setVehicleFullImg("")
        vehicleRepository.queryByPk(_) >> vehVehiclePO
        when:
        def res = approveCertificateCheckListener.vehApproveTemporaryToOfficial(1L,"11")
        then:
        res!=null
    }


    def "test vehApproveTemporaryToOfficial 4"(){
        given:
        def vehVehiclePO = new VehVehiclePO()
        vehVehiclePO.setTemporaryDispatchMark(1)
        vehVehiclePO.setVehicleFullImg("111")
        vehicleRepository.queryByPk(_) >> vehVehiclePO
        vehicleRepository.updateTemporaryDispatchMark(_, _, _) >> 0
        when:
        def res = approveCertificateCheckListener.vehApproveTemporaryToOfficial(1L,"11")
        then:
        res!=null
    }

    def "test drvEmailInfo 1"(){
        given:
        def drvdriverPO = new DrvDriverPO()
        drvdriverPO.setTemporaryDispatchMark(1)
        drvdriverPO.setSupplierId(1L)
        drvDrvierRepository.queryByPk(_) >> drvdriverPO
        enumService.getSupplierEmail(_) >> "111"
        oversaasQconfig.getTemporaryReplenishInfohreshold() >> 1
        when:
        def res = approveCertificateCheckListener.drvEmailInfo(1L)
        then:
        res!=null
    }

    def "test drvEmailInfo 2"(){
        given:
        def drvdriverPO = new DrvDriverPO()
        drvdriverPO.setTemporaryDispatchMark(0)
        when:
        def res = approveCertificateCheckListener.drvEmailInfo(1L)
        then:
        res!=null
    }

    def "test drvAndVehEmailInfo 1"(){
        given:
        def drvdriverPO = new DrvDriverPO()
        drvdriverPO.setTemporaryDispatchMark(1)
        drvdriverPO.setSupplierId(1L)
        drvdriverPO.setDrvId(1L)
        drvdriverPO.setDrvName("111")
        drvDrvierRepository.queryByPk(_) >> drvdriverPO
        enumService.getSupplierEmail(_) >> "111"
        oversaasQconfig.getTemporaryReplenishInfohreshold() >> 1
         def vehiclePO = new VehVehiclePO()
        vehiclePO.setVehicleId(1L)
        vehiclePO.setTemporaryDispatchMark(1)
        vehiclePO.setVehicleLicense("1111")
        vehicleRepository.queryByPk(1L) >> vehiclePO
        when:
        def res = approveCertificateCheckListener.drvAndVehEmailInfo(1L,1L)
        then:
        res!=null
    }

    def "test drvAndVehEmailInfo 2"(){
        given:
        def drvdriverPO = new DrvDriverPO()
        drvdriverPO.setTemporaryDispatchMark(1)
        drvdriverPO.setSupplierId(1L)
        drvdriverPO.setDrvId(1L)
        drvdriverPO.setDrvName("111")
        drvDrvierRepository.queryByPk(_) >> drvdriverPO
        enumService.getSupplierEmail(_) >> "111"
        oversaasQconfig.getTemporaryReplenishInfohreshold() >> 1
        vehicleRepository.queryByPk(1L) >> null
        when:
        def res = approveCertificateCheckListener.drvAndVehEmailInfo(1L,1L)
        then:
        res!=null
    }


    def "test vehEmailInfo 1"(){
        given:
        def vehiclePO = new VehVehiclePO()
        vehiclePO.setTemporaryDispatchMark(1)
        vehiclePO.setVehicleLicense("111")
        vehiclePO.setVehicleId(1L)
        vehicleRepository.queryByPk(_) >> vehiclePO
        enumService.getSupplierEmail(_) >> "111"
        oversaasQconfig.getTemporaryReplenishInfohreshold() >> 1
        when:
        def res = approveCertificateCheckListener.vehEmailInfo(1L)
        then:
        res!=null
    }

    def "test vehEmailInfo 2"(){
        given:
        def vehiclePO = new VehVehiclePO()
        vehiclePO.setTemporaryDispatchMark(0)
        when:
        def res = approveCertificateCheckListener.vehEmailInfo(1L)
        then:
        res!=null
    }
}
