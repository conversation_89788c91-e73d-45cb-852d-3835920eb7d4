package com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice

import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasRequestType
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasResponseType
import com.ctrip.dcs.tms.transport.api.saas.SaasVehicleSoaDTO
import com.ctrip.dcs.tms.transport.application.dto.SaasVehicleDTO
import com.ctrip.dcs.tms.transport.application.query.IQueryVehicleForSaasService
import spock.lang.*
import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.*


class QueryVehicleForSaasExecutorServiceTest extends Specification {
    def testObj = new QueryVehicleForSaasExecutorService()
    def queryVehicleForSaasService = Mock(IQueryVehicleForSaasService)

    def setup() {

        testObj.queryVehicleForSaasService = queryVehicleForSaasService
    }


    @Unroll
    def "executeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        queryVehicleForSaasService.query(_) >> [new SaasVehicleDTO(vehicleId: 1L, supplerId: 1L, vehicleStatus: 0, vehicleLicense: "vehicleLicense", vehicleBrandId: 1L, vehicleBrandName: "vehicleBrandName", vehicleSeriesId: 1L, vehicleSeriesName: "vehicleSeriesName", vehicleTypeId: 1L, vehicleTypeName: "vehicleTypeName", vehicleCategory: 0, vehicleColorId: 1L, vehicleColorName: "vehicleColorName")]


        when:
        def result = testObj.execute(requestType)

        then: "验证返回结果里属性值是否符合预期"

        result.vehicleList == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                                          || expectedResult
        new QueryVehicleForSaasRequestType(vehicleIds: [1L]) || [new SaasVehicleSoaDTO(vehicleId: 1L, supplerId: 1L, vehicleStatus: 0, vehicleLicense: "vehicleLicense", vehicleBrandId: 1L, vehicleBrandName: "vehicleBrandName", vehicleSeriesId: 1L, vehicleSeriesName: "vehicleSeriesName", vehicleTypeId: 1L, vehicleTypeName: "vehicleTypeName", vehicleCategory: 0, vehicleColorId: 1L, vehicleColorName: "vehicleColorName")]
    }


}

