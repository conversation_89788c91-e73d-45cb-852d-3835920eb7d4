package base


import com.ctrip.dcs.tms.transport.infrastructure.common.util.SharkUtils

import com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.clogging.LoggerFactory
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification

/**
 * Created by IntelliJ IDEA.
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2023/6/29 16:22
 */
/**
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2023/4/27 20:14
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest([SharkUtils.class, LoggerFactory.class, TmsTransUtil.class])
@PowerMockIgnore(["javax.management.*", "javax.script.*", "javax.xml.*", "org.xml.*", "org.w3c.dom.*", "jdk.xml.*", "com.sun.org.apache.*"])
class BaseTest extends Specification {
    //初始化静态类mock
    void setup(){
        def loggerMock = Mock(Logger)
        PowerMockito.mockStatic(LoggerFactory.class)
        PowerMockito.mockStatic(TmsTransUtil.class)
        PowerMockito.mockStatic(SharkUtils.class)
        PowerMockito.when(LoggerFactory.getLogger(Mockito.anyString())).thenReturn(loggerMock)
        PowerMockito.when(TmsTransUtil.decrypt(Mockito.any(),Mockito.any())).thenReturn("")
        PowerMockito.when(TmsTransUtil.encrypt(Mockito.any(),Mockito.any())).thenReturn("111")
        PowerMockito.when(SharkUtils.getSharkValueDefault(Mockito.anyString())).thenReturn("111")
    }
}